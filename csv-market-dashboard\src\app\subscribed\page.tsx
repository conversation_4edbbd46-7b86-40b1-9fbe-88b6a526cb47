'use client';

import { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';

interface MarketData {
  securityId: string;
  symbol: string;
  exchange: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high?: number;
  low?: number;
  open?: number;
  close?: number;
  timestamp: number;
  bid?: number;
  ask?: number;
  bidQty?: number;
  askQty?: number;
  openInterest?: number;
  previousOI?: number;
}

export default function SubscribedDashboard() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(new Map());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'symbol' | 'ltp' | 'change' | 'volume' | 'openInterest'>('symbol');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterType, setFilterType] = useState<'all' | 'options' | 'futures'>('all');

  useEffect(() => {
    const newSocket = io('http://localhost:8080', {
      transports: ['websocket'], // Use WebSocket only, no polling
      upgrade: false,
      rememberUpgrade: false
    });
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    newSocket.on('marketData', (data: MarketData) => {
      setMarketData(prev => {
        const newMap = new Map(prev);
        newMap.set(data.securityId, data);
        return newMap;
      });
    });

    // Handle subscription disabled messages
    newSocket.on('subscription_disabled', (data: { message: string }) => {
      console.log('Subscription disabled:', data.message);
    });

    return () => {
      newSocket.close();
    };
  }, []);

  // Get all subscribed data (server auto-subscribed)
  const subscribedData = Array.from(marketData.values());

  // Filter and sort data
  const filteredData = subscribedData
    .filter(data => {
      const matchesSearch = data.symbol.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || 
        (filterType === 'options' && (data.symbol.includes('-CE') || data.symbol.includes('-PE'))) ||
        (filterType === 'futures' && !data.symbol.includes('-CE') && !data.symbol.includes('-PE'));
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      let aVal: any, bVal: any;
      
      switch (sortBy) {
        case 'symbol':
          aVal = a.symbol;
          bVal = b.symbol;
          break;
        case 'ltp':
          aVal = a.ltp || 0;
          bVal = b.ltp || 0;
          break;
        case 'change':
          aVal = a.change || 0;
          bVal = b.change || 0;
          break;
        case 'volume':
          aVal = a.volume || 0;
          bVal = b.volume || 0;
          break;
        case 'openInterest':
          aVal = a.openInterest || 0;
          bVal = b.openInterest || 0;
          break;
        default:
          aVal = a.symbol;
          bVal = b.symbol;
      }

      if (typeof aVal === 'string') {
        return sortOrder === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      } else {
        return sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
      }
    });

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const formatNumber = (num: number | undefined) => {
    if (!num) return 'N/A';
    return num.toLocaleString();
  };

  const formatPrice = (price: number | undefined) => {
    if (!price) return 'N/A';
    return `₹${price.toFixed(2)}`;
  };

  const getChangeColor = (change: number | undefined) => {
    if (!change) return 'text-gray-500';
    return change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-500';
  };

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">NSE Derivatives - Subscribed Data Dashboard</h1>
        <div className="space-x-3">
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors"
          >
            🏠 Main Dashboard
          </a>
          <a
            href="/option-chain"
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
          >
            🔗 Option Chain
          </a>
        </div>
      </div>
      
      {/* Status Bar */}
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`inline-block px-3 py-1 rounded ${connected ? 'bg-green-500' : 'bg-red-500'} text-white`}>
              {connected ? '🟢 Connected' : '🔴 Disconnected'}
            </div>
            <span className="text-gray-700">
              📊 Total Subscribed: {subscribedData.length} instruments
            </span>
            <span className="text-gray-700">
              📈 Live Data: {subscribedData.filter(d => d.ltp > 0).length} active
            </span>
            <span className="text-gray-700">
              🔢 With OI: {subscribedData.filter(d => d.openInterest && d.openInterest > 0).length} contracts
            </span>
          </div>
          <div className="text-sm text-gray-600">
            Server Auto-Subscription: NSE OPTIDX + FUTIDX Only
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search Instruments</label>
            <input
              type="text"
              placeholder="Search by symbol..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Instruments</option>
              <option value="options">Options (CE/PE)</option>
              <option value="futures">Futures</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Showing Results</label>
            <div className="p-2 bg-gray-50 rounded-md text-sm">
              {filteredData.length} of {subscribedData.length} instruments
            </div>
          </div>
        </div>
      </div>

      {/* Market Data Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('symbol')}
                >
                  Symbol {sortBy === 'symbol' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Exchange
                </th>
                <th 
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('ltp')}
                >
                  LTP {sortBy === 'ltp' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th 
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('change')}
                >
                  Change {sortBy === 'change' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th 
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('volume')}
                >
                  Volume {sortBy === 'volume' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th 
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('openInterest')}
                >
                  Open Interest {sortBy === 'openInterest' && (sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  High/Low
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Open/Close
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.slice(0, 100).map((data) => (
                <tr key={data.securityId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {data.symbol}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {data.exchange}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                    {formatPrice(data.ltp)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getChangeColor(data.change)}`}>
                    {data.change ? (
                      <>
                        {data.change > 0 ? '+' : ''}{data.change.toFixed(2)}
                        <br />
                        <span className="text-xs">
                          ({data.changePercent > 0 ? '+' : ''}{data.changePercent.toFixed(2)}%)
                        </span>
                      </>
                    ) : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                    {formatNumber(data.volume)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-blue-600">
                    {formatNumber(data.openInterest)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                    {data.high ? (
                      <>
                        {formatPrice(data.high)}
                        <br />
                        <span className="text-xs">{formatPrice(data.low)}</span>
                      </>
                    ) : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                    {data.open ? (
                      <>
                        {formatPrice(data.open)}
                        <br />
                        <span className="text-xs">{formatPrice(data.close)}</span>
                      </>
                    ) : 'N/A'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredData.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No subscribed data available</p>
            <p className="text-sm text-gray-400 mt-2">
              The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments
            </p>
          </div>
        )}
        
        {filteredData.length > 100 && (
          <div className="bg-gray-50 px-6 py-3 text-sm text-gray-600">
            Showing first 100 of {filteredData.length} results. Use search to narrow down.
          </div>
        )}
      </div>
    </div>
  );
}
