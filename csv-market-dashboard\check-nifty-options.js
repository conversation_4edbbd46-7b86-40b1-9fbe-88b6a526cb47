// <PERSON>rip<PERSON> to examine NIFTY option data format
const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 Examining NIFTY option data format...');

const niftyOptions = [];
let count = 0;

fs.createReadStream('./instruments.csv')
  .pipe(csv())
  .on('data', (data) => {
    count++;
    
    // Look for NIFTY options specifically
    if (data.SYMBOL_NAME && data.SYMBOL_NAME.includes('NIFTY') && 
        data.INSTRUMENT === 'OPTIDX' && 
        ['CE', 'PE'].includes(data.OPTION_TYPE)) {
      
      niftyOptions.push({
        SECURITY_ID: data.SECURITY_ID,
        SYMBOL_NAME: data.SYMBOL_NAME,
        SM_EXPIRY_DATE: data.SM_EXPIRY_DATE,
        STRIKE_PRICE: data.STRIKE_PRICE,
        OPTION_TYPE: data.OPTION_TYPE,
        INSTRUMENT: data.INSTRUMENT,
        UNDERLYING_SYMBOL: data.UNDERLYING_SYMBOL
      });
    }
  })
  .on('end', () => {
    console.log(`\n✅ Total rows processed: ${count}`);
    console.log(`🎯 NIFTY options found: ${niftyOptions.length}`);
    
    if (niftyOptions.length > 0) {
      console.log('\n📊 Sample NIFTY options:');
      niftyOptions.slice(0, 20).forEach((option, index) => {
        console.log(`${index + 1}. ${option.SYMBOL_NAME}`);
        console.log(`   Security ID: ${option.SECURITY_ID}`);
        console.log(`   Expiry: ${option.SM_EXPIRY_DATE}`);
        console.log(`   Strike: ${option.STRIKE_PRICE}`);
        console.log(`   Type: ${option.OPTION_TYPE}`);
        console.log(`   Underlying: ${option.UNDERLYING_SYMBOL}`);
        console.log('');
      });
      
      // Group by expiry date
      const expiryGroups = {};
      niftyOptions.forEach(option => {
        const expiry = option.SM_EXPIRY_DATE;
        if (!expiryGroups[expiry]) {
          expiryGroups[expiry] = [];
        }
        expiryGroups[expiry].push(option);
      });
      
      console.log('\n📅 NIFTY options by expiry date:');
      Object.keys(expiryGroups).sort().slice(0, 5).forEach(expiry => {
        const options = expiryGroups[expiry];
        console.log(`   ${expiry}: ${options.length} options`);
        
        // Show sample strikes for this expiry
        const strikes = [...new Set(options.map(o => o.STRIKE_PRICE))].sort((a, b) => parseFloat(a) - parseFloat(b));
        console.log(`     Strikes: ${strikes.slice(0, 10).join(', ')}${strikes.length > 10 ? '...' : ''}`);
      });
      
      // Show option type distribution
      const typeGroups = {};
      niftyOptions.forEach(option => {
        const type = option.OPTION_TYPE;
        typeGroups[type] = (typeGroups[type] || 0) + 1;
      });
      
      console.log('\n📈 Option type distribution:');
      Object.entries(typeGroups).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`);
      });
    }
  })
  .on('error', (error) => {
    console.error('❌ Error reading CSV:', error);
  });
