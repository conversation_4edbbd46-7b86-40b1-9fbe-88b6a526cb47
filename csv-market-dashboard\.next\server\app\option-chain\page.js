/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/option-chain/page";
exports.ids = ["app/option-chain/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'option-chain',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(rsc)/./src/app/option-chain/page.tsx\")), \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/option-chain/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/option-chain/page\",\n        pathname: \"/option-chain\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(ssr)/./src/app/option-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsaWtlJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcHRpb24tY2hhaW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvPzg5MTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsaWtlXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcb3B0aW9uLWNoYWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChainPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_OptionChain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/OptionChain */ \"(ssr)/./src/components/OptionChain.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction OptionChainPage() {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:8080\", {\n            transports: [\n                \"websocket\"\n            ],\n            upgrade: false,\n            rememberUpgrade: false\n        });\n        setSocket(newSocket);\n        newSocket.on(\"connect\", ()=>{\n            console.log(\"Connected to server for option chain\");\n            setConnected(true);\n        });\n        newSocket.on(\"disconnect\", ()=>{\n            console.log(\"Disconnected from server\");\n            setConnected(false);\n        });\n        newSocket.on(\"marketData\", (data)=>{\n            setMarketData((prev)=>{\n                const newMap = new Map(prev);\n                newMap.set(data.securityId, data);\n                return newMap;\n            });\n        });\n        // Handle subscription disabled messages\n        newSocket.on(\"subscription_disabled\", (data)=>{\n            console.log(\"Subscription disabled:\", data.message);\n        });\n        return ()=>{\n            newSocket.close();\n        };\n    }, []);\n    // Get all subscribed data (server auto-subscribed)\n    const subscribedData = Array.from(marketData.values());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"← Main Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/subscribed\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"\\uD83D\\uDCCA Subscribed Data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${connected ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-2 h-2 rounded-full mr-1 ${connected ? \"bg-green-400\" : \"bg-red-400\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        connected ? \"Connected\" : \"Disconnected\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        subscribedData.length,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        subscribedData.filter((d)=>d.ltp > 0).length,\n                                        \" active\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionChain__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                marketData: marketData\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/option-chain/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChain)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction OptionChain({ marketData }) {\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(`Failed to fetch expiry dates: ${response.statusText}`);\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDCC5 Fetched expiry data:\", result);\n            if (result.success) {\n                setExpiryData(result.data);\n                console.log(\"\\uD83D\\uDCCB Available expiry dates:\", result.data.expiries);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    const firstExpiry = result.data.expiries[0];\n                    console.log(\"\\uD83C\\uDFAF Auto-selecting first expiry:\", firstExpiry);\n                    setSelectedExpiry(firstExpiry);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        console.log(\"\\uD83D\\uDCCA Total market data entries:\", marketData.size);\n        // Debug: Show sample market data entries\n        const sampleEntries = Array.from(marketData.entries()).slice(0, 10);\n        console.log(\"\\uD83D\\uDCCB Sample market data entries:\", sampleEntries.map(([id, data])=>({\n                securityId: id,\n                symbol: data.symbol,\n                expiryDate: data.expiryDate,\n                optionType: data.optionType,\n                strikePrice: data.strikePrice\n            })));\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        console.log(\"\\uD83C\\uDFAF Generated strikes:\", strikes);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        console.log(\"\\uD83D\\uDCC8 Option chain rows built:\", rows.length, \"rows\");\n        console.log(\"✅ Found options:\", rows.filter((r)=>r.call || r.put).length, \"strikes with data\");\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(`[STRIKE] 🎯 ATM Strike identified: ${atmStrike} (Spot: ${spotPrice})`);\n        console.log(`[STRIKE] 📊 Available strikes: ${allStrikes.length}`);\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(`[STRIKE] ✅ Selected ${selectedStrikes.length} strikes around ATM:`, selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        console.log(`[SEARCH] Looking for ${optionType} ${strike} with expiry ${selectedExpiry}`);\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(`-${strike}-${optionType}`) && data.expiryDate === selectedExpiry) {\n                console.log(`[OPTION] ✅ Found ${optionType} ${strike}: ${data.symbol} (Expiry: ${data.expiryDate})`);\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: data.marketDepth?.[0]?.bidPrice || data.bid,\n                    ask: data.marketDepth?.[0]?.askPrice || data.ask,\n                    bidQty: data.marketDepth?.[0]?.bidQty || data.bidQty,\n                    askQty: data.marketDepth?.[0]?.askQty || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        console.log(`[OPTION] ❌ Not found ${optionType} ${strike} for expiry ${selectedExpiry}`);\n        // Show available NIFTY options for debugging\n        const niftyOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\")).slice(0, 10);\n        console.log(`[DEBUG] Available NIFTY options (${niftyOptions.length} total):`, niftyOptions.map((opt)=>`${opt.symbol} (Expiry: ${opt.expiryDate}, Strike: ${opt.strikePrice}, Type: ${opt.optionType})`));\n        // Show specific type options\n        const typeOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(`-${optionType}`)).slice(0, 5);\n        console.log(`[DEBUG] Available ${optionType} options:`, typeOptions.map((opt)=>`${opt.symbol} (Expiry: ${opt.expiryDate})`));\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return `${months[date.getMonth()]}${date.getFullYear()}`;\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return `₹${price.toFixed(2)}`;\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: `flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && ` ${expiryYear.toString().slice(-2)}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        optionChain?.rows.length || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex hover:bg-gray-50 transition-colors ${isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.call?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.call?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.call?.change)}`,\n                                        children: row.call?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold ${isITM_Call ? \"text-green-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.call?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-xs font-mono border-r border-gray-200 ${isITM_Call ? \"text-green-600\" : \"text-gray-500\"}`,\n                                        children: row.call?.securityId || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"}`,\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-xs font-mono ${isITM_Put ? \"text-red-600\" : \"text-gray-500\"}`,\n                                        children: row.put?.securityId || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold ${isITM_Put ? \"text-red-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.put?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.put?.change)}`,\n                                        children: row.put?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.put?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.put?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OptionChain.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"085ee3861088\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzZmZWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwODVlZTM4NjEwODhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"CSV Market Dashboard\",\n    description: \"Real-time market data dashboard using CSV instrument subscription\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQzlCLDRFQUFDVTtnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ1NWIE1hcmtldCBEYXNoYm9hcmQnLFxuICBkZXNjcmlwdGlvbjogJ1JlYWwtdGltZSBtYXJrZXQgZGF0YSBkYXNoYm9hcmQgdXNpbmcgQ1NWIGluc3RydW1lbnQgc3Vic2NyaXB0aW9uJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHZpYS1ibHVlLTUwLzMwIHRvLWluZGlnby01MC8yMFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\like\csv-market-dashboard\src\app\option-chain\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();