/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/option-chain/page";
exports.ids = ["app/option-chain/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'option-chain',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(rsc)/./src/app/option-chain/page.tsx\")), \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/option-chain/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/option-chain/page\",\n        pathname: \"/option-chain\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/option-chain/page.tsx */ \"(ssr)/./src/app/option-chain/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNsaWtlJTVDJTVDY3N2LW1hcmtldC1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcHRpb24tY2hhaW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvPzg5MTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxsaWtlXFxcXGNzdi1tYXJrZXQtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcb3B0aW9uLWNoYWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Clike%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Coption-chain%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChainPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_OptionChain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/OptionChain */ \"(ssr)/./src/components/OptionChain.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction OptionChainPage() {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:8080\", {\n            transports: [\n                \"websocket\"\n            ],\n            upgrade: false,\n            rememberUpgrade: false\n        });\n        setSocket(newSocket);\n        newSocket.on(\"connect\", ()=>{\n            console.log(\"Connected to server for option chain\");\n            setConnected(true);\n        });\n        newSocket.on(\"disconnect\", ()=>{\n            console.log(\"Disconnected from server\");\n            setConnected(false);\n        });\n        newSocket.on(\"marketData\", (data)=>{\n            setMarketData((prev)=>{\n                const newMap = new Map(prev);\n                newMap.set(data.securityId, data);\n                return newMap;\n            });\n        });\n        // Handle subscription disabled messages\n        newSocket.on(\"subscription_disabled\", (data)=>{\n            console.log(\"Subscription disabled:\", data.message);\n        });\n        return ()=>{\n            newSocket.close();\n        };\n    }, []);\n    // Get all subscribed data (server auto-subscribed)\n    const subscribedData = Array.from(marketData.values());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"← Main Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/subscribed\",\n                                    className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors\",\n                                    children: \"\\uD83D\\uDCCA Subscribed Data\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${connected ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-2 h-2 rounded-full mr-1 ${connected ? \"bg-green-400\" : \"bg-red-400\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        connected ? \"Connected\" : \"Disconnected\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        subscribedData.length,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        subscribedData.filter((d)=>d.ltp > 0).length,\n                                        \" active\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptionChain__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                marketData: marketData\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\option-chain\\\\page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/option-chain/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptionChain)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction OptionChain({ marketData }) {\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(`Failed to fetch expiry dates: ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(`[STRIKE] 🎯 ATM Strike identified: ${atmStrike} (Spot: ${spotPrice})`);\n        console.log(`[STRIKE] 📊 Available strikes: ${allStrikes.length}`);\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(`[STRIKE] ✅ Selected ${selectedStrikes.length} strikes around ATM:`, selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(`-${strike}-${optionType}`) && data.expiryDate === selectedExpiry) {\n                console.log(`[OPTION] ✅ Found ${optionType} ${strike}: ${data.symbol} (Expiry: ${data.expiryDate})`);\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: data.marketDepth?.[0]?.bidPrice || data.bid,\n                    ask: data.marketDepth?.[0]?.askPrice || data.ask,\n                    bidQty: data.marketDepth?.[0]?.bidQty || data.bidQty,\n                    askQty: data.marketDepth?.[0]?.askQty || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(`[OPTION] ❌ Not found ${optionType} ${strike} for expiry ${selectedExpiry}`);\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(`-${optionType}`)).slice(0, 5);\n            console.log(`[DEBUG] Available ${optionType} options:`, availableOptions.map((opt)=>`${opt.symbol} (Expiry: ${opt.expiryDate})`));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return `${months[date.getMonth()]}${date.getFullYear()}`;\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return `₹${price.toFixed(2)}`;\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: `flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && ` ${expiryYear.toString().slice(-2)}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        optionChain?.rows.length || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex hover:bg-gray-50 transition-colors ${isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.call?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.call?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.call?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.call?.change)}`,\n                                        children: row.call?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isITM_Call ? \"text-green-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.call?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"}`,\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-bold ${isITM_Put ? \"text-red-600\" : \"text-gray-700\"}`,\n                                        children: formatPrice(row.put?.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm font-medium ${getChangeColor(row.put?.change)}`,\n                                        children: row.put?.change ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice(row.put?.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice(row.put?.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex-1 text-center py-3 px-1 text-sm ${isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"}`,\n                                        children: formatNumber(row.put?.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 501,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OptionChain.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"085ee3861088\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzZmZWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwODVlZTM4NjEwODhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"CSV Market Dashboard\",\n    description: \"Real-time market data dashboard using CSV instrument subscription\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQzlCLDRFQUFDVTtnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ1NWIE1hcmtldCBEYXNoYm9hcmQnLFxuICBkZXNjcmlwdGlvbjogJ1JlYWwtdGltZSBtYXJrZXQgZGF0YSBkYXNoYm9hcmQgdXNpbmcgQ1NWIGluc3RydW1lbnQgc3Vic2NyaXB0aW9uJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHZpYS1ibHVlLTUwLzMwIHRvLWluZGlnby01MC8yMFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/option-chain/page.tsx":
/*!***************************************!*\
  !*** ./src/app/option-chain/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\like\csv-market-dashboard\src\app\option-chain\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Foption-chain%2Fpage&page=%2Foption-chain%2Fpage&appPaths=%2Foption-chain%2Fpage&pagePath=private-next-app-dir%2Foption-chain%2Fpage.tsx&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();