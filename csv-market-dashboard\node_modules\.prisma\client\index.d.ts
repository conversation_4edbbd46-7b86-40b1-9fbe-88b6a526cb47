
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Instruments
 * 
 */
export type Instruments = $Result.DefaultSelection<Prisma.$InstrumentsPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Instruments
 * const instruments = await prisma.instruments.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Instruments
   * const instruments = await prisma.instruments.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.instruments`: Exposes CRUD operations for the **Instruments** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Instruments
    * const instruments = await prisma.instruments.findMany()
    * ```
    */
  get instruments(): Prisma.InstrumentsDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.11.1
   * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Instruments: 'Instruments'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "instruments"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Instruments: {
        payload: Prisma.$InstrumentsPayload<ExtArgs>
        fields: Prisma.InstrumentsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.InstrumentsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.InstrumentsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          findFirst: {
            args: Prisma.InstrumentsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.InstrumentsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          findMany: {
            args: Prisma.InstrumentsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>[]
          }
          create: {
            args: Prisma.InstrumentsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          createMany: {
            args: Prisma.InstrumentsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.InstrumentsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>[]
          }
          delete: {
            args: Prisma.InstrumentsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          update: {
            args: Prisma.InstrumentsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          deleteMany: {
            args: Prisma.InstrumentsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.InstrumentsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.InstrumentsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>[]
          }
          upsert: {
            args: Prisma.InstrumentsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$InstrumentsPayload>
          }
          aggregate: {
            args: Prisma.InstrumentsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateInstruments>
          }
          groupBy: {
            args: Prisma.InstrumentsGroupByArgs<ExtArgs>
            result: $Utils.Optional<InstrumentsGroupByOutputType>[]
          }
          count: {
            args: Prisma.InstrumentsCountArgs<ExtArgs>
            result: $Utils.Optional<InstrumentsCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    instruments?: InstrumentsOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model Instruments
   */

  export type AggregateInstruments = {
    _count: InstrumentsCountAggregateOutputType | null
    _min: InstrumentsMinAggregateOutputType | null
    _max: InstrumentsMaxAggregateOutputType | null
  }

  export type InstrumentsMinAggregateOutputType = {
    EXCH_ID: string | null
    SEGMENT: string | null
    SECURITY_ID: string | null
    ISIN: string | null
    INSTRUMENT: string | null
    UNDERLYING_SECURITY_ID: string | null
    UNDERLYING_SYMBOL: string | null
    SYMBOL_NAME: string | null
    DISPLAY_NAME: string | null
    INSTRUMENT_TYPE: string | null
    STRIKE_PRICE: string | null
    OPTION_TYPE: string | null
    SM_EXPIRY_DATE: string | null
    LOT_SIZE: string | null
    TICK_SIZE: string | null
    MULTIPLIER: string | null
    FREEZE_QTY: string | null
    WARNING_QTY: string | null
    DAILY_PRICE_BAND_LOWER: string | null
    DAILY_PRICE_BAND_UPPER: string | null
    TRADE_PRICE_BAND_LOWER: string | null
    TRADE_PRICE_BAND_UPPER: string | null
    MTF_LEVERAGE: string | null
  }

  export type InstrumentsMaxAggregateOutputType = {
    EXCH_ID: string | null
    SEGMENT: string | null
    SECURITY_ID: string | null
    ISIN: string | null
    INSTRUMENT: string | null
    UNDERLYING_SECURITY_ID: string | null
    UNDERLYING_SYMBOL: string | null
    SYMBOL_NAME: string | null
    DISPLAY_NAME: string | null
    INSTRUMENT_TYPE: string | null
    STRIKE_PRICE: string | null
    OPTION_TYPE: string | null
    SM_EXPIRY_DATE: string | null
    LOT_SIZE: string | null
    TICK_SIZE: string | null
    MULTIPLIER: string | null
    FREEZE_QTY: string | null
    WARNING_QTY: string | null
    DAILY_PRICE_BAND_LOWER: string | null
    DAILY_PRICE_BAND_UPPER: string | null
    TRADE_PRICE_BAND_LOWER: string | null
    TRADE_PRICE_BAND_UPPER: string | null
    MTF_LEVERAGE: string | null
  }

  export type InstrumentsCountAggregateOutputType = {
    EXCH_ID: number
    SEGMENT: number
    SECURITY_ID: number
    ISIN: number
    INSTRUMENT: number
    UNDERLYING_SECURITY_ID: number
    UNDERLYING_SYMBOL: number
    SYMBOL_NAME: number
    DISPLAY_NAME: number
    INSTRUMENT_TYPE: number
    STRIKE_PRICE: number
    OPTION_TYPE: number
    SM_EXPIRY_DATE: number
    LOT_SIZE: number
    TICK_SIZE: number
    MULTIPLIER: number
    FREEZE_QTY: number
    WARNING_QTY: number
    DAILY_PRICE_BAND_LOWER: number
    DAILY_PRICE_BAND_UPPER: number
    TRADE_PRICE_BAND_LOWER: number
    TRADE_PRICE_BAND_UPPER: number
    MTF_LEVERAGE: number
    _all: number
  }


  export type InstrumentsMinAggregateInputType = {
    EXCH_ID?: true
    SEGMENT?: true
    SECURITY_ID?: true
    ISIN?: true
    INSTRUMENT?: true
    UNDERLYING_SECURITY_ID?: true
    UNDERLYING_SYMBOL?: true
    SYMBOL_NAME?: true
    DISPLAY_NAME?: true
    INSTRUMENT_TYPE?: true
    STRIKE_PRICE?: true
    OPTION_TYPE?: true
    SM_EXPIRY_DATE?: true
    LOT_SIZE?: true
    TICK_SIZE?: true
    MULTIPLIER?: true
    FREEZE_QTY?: true
    WARNING_QTY?: true
    DAILY_PRICE_BAND_LOWER?: true
    DAILY_PRICE_BAND_UPPER?: true
    TRADE_PRICE_BAND_LOWER?: true
    TRADE_PRICE_BAND_UPPER?: true
    MTF_LEVERAGE?: true
  }

  export type InstrumentsMaxAggregateInputType = {
    EXCH_ID?: true
    SEGMENT?: true
    SECURITY_ID?: true
    ISIN?: true
    INSTRUMENT?: true
    UNDERLYING_SECURITY_ID?: true
    UNDERLYING_SYMBOL?: true
    SYMBOL_NAME?: true
    DISPLAY_NAME?: true
    INSTRUMENT_TYPE?: true
    STRIKE_PRICE?: true
    OPTION_TYPE?: true
    SM_EXPIRY_DATE?: true
    LOT_SIZE?: true
    TICK_SIZE?: true
    MULTIPLIER?: true
    FREEZE_QTY?: true
    WARNING_QTY?: true
    DAILY_PRICE_BAND_LOWER?: true
    DAILY_PRICE_BAND_UPPER?: true
    TRADE_PRICE_BAND_LOWER?: true
    TRADE_PRICE_BAND_UPPER?: true
    MTF_LEVERAGE?: true
  }

  export type InstrumentsCountAggregateInputType = {
    EXCH_ID?: true
    SEGMENT?: true
    SECURITY_ID?: true
    ISIN?: true
    INSTRUMENT?: true
    UNDERLYING_SECURITY_ID?: true
    UNDERLYING_SYMBOL?: true
    SYMBOL_NAME?: true
    DISPLAY_NAME?: true
    INSTRUMENT_TYPE?: true
    STRIKE_PRICE?: true
    OPTION_TYPE?: true
    SM_EXPIRY_DATE?: true
    LOT_SIZE?: true
    TICK_SIZE?: true
    MULTIPLIER?: true
    FREEZE_QTY?: true
    WARNING_QTY?: true
    DAILY_PRICE_BAND_LOWER?: true
    DAILY_PRICE_BAND_UPPER?: true
    TRADE_PRICE_BAND_LOWER?: true
    TRADE_PRICE_BAND_UPPER?: true
    MTF_LEVERAGE?: true
    _all?: true
  }

  export type InstrumentsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Instruments to aggregate.
     */
    where?: InstrumentsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Instruments to fetch.
     */
    orderBy?: InstrumentsOrderByWithRelationInput | InstrumentsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: InstrumentsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Instruments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Instruments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Instruments
    **/
    _count?: true | InstrumentsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: InstrumentsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: InstrumentsMaxAggregateInputType
  }

  export type GetInstrumentsAggregateType<T extends InstrumentsAggregateArgs> = {
        [P in keyof T & keyof AggregateInstruments]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateInstruments[P]>
      : GetScalarType<T[P], AggregateInstruments[P]>
  }




  export type InstrumentsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: InstrumentsWhereInput
    orderBy?: InstrumentsOrderByWithAggregationInput | InstrumentsOrderByWithAggregationInput[]
    by: InstrumentsScalarFieldEnum[] | InstrumentsScalarFieldEnum
    having?: InstrumentsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: InstrumentsCountAggregateInputType | true
    _min?: InstrumentsMinAggregateInputType
    _max?: InstrumentsMaxAggregateInputType
  }

  export type InstrumentsGroupByOutputType = {
    EXCH_ID: string
    SEGMENT: string
    SECURITY_ID: string
    ISIN: string | null
    INSTRUMENT: string
    UNDERLYING_SECURITY_ID: string | null
    UNDERLYING_SYMBOL: string | null
    SYMBOL_NAME: string
    DISPLAY_NAME: string | null
    INSTRUMENT_TYPE: string | null
    STRIKE_PRICE: string | null
    OPTION_TYPE: string | null
    SM_EXPIRY_DATE: string | null
    LOT_SIZE: string | null
    TICK_SIZE: string | null
    MULTIPLIER: string | null
    FREEZE_QTY: string | null
    WARNING_QTY: string | null
    DAILY_PRICE_BAND_LOWER: string | null
    DAILY_PRICE_BAND_UPPER: string | null
    TRADE_PRICE_BAND_LOWER: string | null
    TRADE_PRICE_BAND_UPPER: string | null
    MTF_LEVERAGE: string | null
    _count: InstrumentsCountAggregateOutputType | null
    _min: InstrumentsMinAggregateOutputType | null
    _max: InstrumentsMaxAggregateOutputType | null
  }

  type GetInstrumentsGroupByPayload<T extends InstrumentsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<InstrumentsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof InstrumentsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], InstrumentsGroupByOutputType[P]>
            : GetScalarType<T[P], InstrumentsGroupByOutputType[P]>
        }
      >
    >


  export type InstrumentsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    EXCH_ID?: boolean
    SEGMENT?: boolean
    SECURITY_ID?: boolean
    ISIN?: boolean
    INSTRUMENT?: boolean
    UNDERLYING_SECURITY_ID?: boolean
    UNDERLYING_SYMBOL?: boolean
    SYMBOL_NAME?: boolean
    DISPLAY_NAME?: boolean
    INSTRUMENT_TYPE?: boolean
    STRIKE_PRICE?: boolean
    OPTION_TYPE?: boolean
    SM_EXPIRY_DATE?: boolean
    LOT_SIZE?: boolean
    TICK_SIZE?: boolean
    MULTIPLIER?: boolean
    FREEZE_QTY?: boolean
    WARNING_QTY?: boolean
    DAILY_PRICE_BAND_LOWER?: boolean
    DAILY_PRICE_BAND_UPPER?: boolean
    TRADE_PRICE_BAND_LOWER?: boolean
    TRADE_PRICE_BAND_UPPER?: boolean
    MTF_LEVERAGE?: boolean
  }, ExtArgs["result"]["instruments"]>

  export type InstrumentsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    EXCH_ID?: boolean
    SEGMENT?: boolean
    SECURITY_ID?: boolean
    ISIN?: boolean
    INSTRUMENT?: boolean
    UNDERLYING_SECURITY_ID?: boolean
    UNDERLYING_SYMBOL?: boolean
    SYMBOL_NAME?: boolean
    DISPLAY_NAME?: boolean
    INSTRUMENT_TYPE?: boolean
    STRIKE_PRICE?: boolean
    OPTION_TYPE?: boolean
    SM_EXPIRY_DATE?: boolean
    LOT_SIZE?: boolean
    TICK_SIZE?: boolean
    MULTIPLIER?: boolean
    FREEZE_QTY?: boolean
    WARNING_QTY?: boolean
    DAILY_PRICE_BAND_LOWER?: boolean
    DAILY_PRICE_BAND_UPPER?: boolean
    TRADE_PRICE_BAND_LOWER?: boolean
    TRADE_PRICE_BAND_UPPER?: boolean
    MTF_LEVERAGE?: boolean
  }, ExtArgs["result"]["instruments"]>

  export type InstrumentsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    EXCH_ID?: boolean
    SEGMENT?: boolean
    SECURITY_ID?: boolean
    ISIN?: boolean
    INSTRUMENT?: boolean
    UNDERLYING_SECURITY_ID?: boolean
    UNDERLYING_SYMBOL?: boolean
    SYMBOL_NAME?: boolean
    DISPLAY_NAME?: boolean
    INSTRUMENT_TYPE?: boolean
    STRIKE_PRICE?: boolean
    OPTION_TYPE?: boolean
    SM_EXPIRY_DATE?: boolean
    LOT_SIZE?: boolean
    TICK_SIZE?: boolean
    MULTIPLIER?: boolean
    FREEZE_QTY?: boolean
    WARNING_QTY?: boolean
    DAILY_PRICE_BAND_LOWER?: boolean
    DAILY_PRICE_BAND_UPPER?: boolean
    TRADE_PRICE_BAND_LOWER?: boolean
    TRADE_PRICE_BAND_UPPER?: boolean
    MTF_LEVERAGE?: boolean
  }, ExtArgs["result"]["instruments"]>

  export type InstrumentsSelectScalar = {
    EXCH_ID?: boolean
    SEGMENT?: boolean
    SECURITY_ID?: boolean
    ISIN?: boolean
    INSTRUMENT?: boolean
    UNDERLYING_SECURITY_ID?: boolean
    UNDERLYING_SYMBOL?: boolean
    SYMBOL_NAME?: boolean
    DISPLAY_NAME?: boolean
    INSTRUMENT_TYPE?: boolean
    STRIKE_PRICE?: boolean
    OPTION_TYPE?: boolean
    SM_EXPIRY_DATE?: boolean
    LOT_SIZE?: boolean
    TICK_SIZE?: boolean
    MULTIPLIER?: boolean
    FREEZE_QTY?: boolean
    WARNING_QTY?: boolean
    DAILY_PRICE_BAND_LOWER?: boolean
    DAILY_PRICE_BAND_UPPER?: boolean
    TRADE_PRICE_BAND_LOWER?: boolean
    TRADE_PRICE_BAND_UPPER?: boolean
    MTF_LEVERAGE?: boolean
  }

  export type InstrumentsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"EXCH_ID" | "SEGMENT" | "SECURITY_ID" | "ISIN" | "INSTRUMENT" | "UNDERLYING_SECURITY_ID" | "UNDERLYING_SYMBOL" | "SYMBOL_NAME" | "DISPLAY_NAME" | "INSTRUMENT_TYPE" | "STRIKE_PRICE" | "OPTION_TYPE" | "SM_EXPIRY_DATE" | "LOT_SIZE" | "TICK_SIZE" | "MULTIPLIER" | "FREEZE_QTY" | "WARNING_QTY" | "DAILY_PRICE_BAND_LOWER" | "DAILY_PRICE_BAND_UPPER" | "TRADE_PRICE_BAND_LOWER" | "TRADE_PRICE_BAND_UPPER" | "MTF_LEVERAGE", ExtArgs["result"]["instruments"]>

  export type $InstrumentsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Instruments"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      EXCH_ID: string
      SEGMENT: string
      SECURITY_ID: string
      ISIN: string | null
      INSTRUMENT: string
      UNDERLYING_SECURITY_ID: string | null
      UNDERLYING_SYMBOL: string | null
      SYMBOL_NAME: string
      DISPLAY_NAME: string | null
      INSTRUMENT_TYPE: string | null
      STRIKE_PRICE: string | null
      OPTION_TYPE: string | null
      SM_EXPIRY_DATE: string | null
      LOT_SIZE: string | null
      TICK_SIZE: string | null
      MULTIPLIER: string | null
      FREEZE_QTY: string | null
      WARNING_QTY: string | null
      DAILY_PRICE_BAND_LOWER: string | null
      DAILY_PRICE_BAND_UPPER: string | null
      TRADE_PRICE_BAND_LOWER: string | null
      TRADE_PRICE_BAND_UPPER: string | null
      MTF_LEVERAGE: string | null
    }, ExtArgs["result"]["instruments"]>
    composites: {}
  }

  type InstrumentsGetPayload<S extends boolean | null | undefined | InstrumentsDefaultArgs> = $Result.GetResult<Prisma.$InstrumentsPayload, S>

  type InstrumentsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<InstrumentsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: InstrumentsCountAggregateInputType | true
    }

  export interface InstrumentsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Instruments'], meta: { name: 'Instruments' } }
    /**
     * Find zero or one Instruments that matches the filter.
     * @param {InstrumentsFindUniqueArgs} args - Arguments to find a Instruments
     * @example
     * // Get one Instruments
     * const instruments = await prisma.instruments.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends InstrumentsFindUniqueArgs>(args: SelectSubset<T, InstrumentsFindUniqueArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Instruments that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {InstrumentsFindUniqueOrThrowArgs} args - Arguments to find a Instruments
     * @example
     * // Get one Instruments
     * const instruments = await prisma.instruments.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends InstrumentsFindUniqueOrThrowArgs>(args: SelectSubset<T, InstrumentsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Instruments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsFindFirstArgs} args - Arguments to find a Instruments
     * @example
     * // Get one Instruments
     * const instruments = await prisma.instruments.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends InstrumentsFindFirstArgs>(args?: SelectSubset<T, InstrumentsFindFirstArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Instruments that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsFindFirstOrThrowArgs} args - Arguments to find a Instruments
     * @example
     * // Get one Instruments
     * const instruments = await prisma.instruments.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends InstrumentsFindFirstOrThrowArgs>(args?: SelectSubset<T, InstrumentsFindFirstOrThrowArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Instruments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Instruments
     * const instruments = await prisma.instruments.findMany()
     * 
     * // Get first 10 Instruments
     * const instruments = await prisma.instruments.findMany({ take: 10 })
     * 
     * // Only select the `EXCH_ID`
     * const instrumentsWithEXCH_IDOnly = await prisma.instruments.findMany({ select: { EXCH_ID: true } })
     * 
     */
    findMany<T extends InstrumentsFindManyArgs>(args?: SelectSubset<T, InstrumentsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Instruments.
     * @param {InstrumentsCreateArgs} args - Arguments to create a Instruments.
     * @example
     * // Create one Instruments
     * const Instruments = await prisma.instruments.create({
     *   data: {
     *     // ... data to create a Instruments
     *   }
     * })
     * 
     */
    create<T extends InstrumentsCreateArgs>(args: SelectSubset<T, InstrumentsCreateArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Instruments.
     * @param {InstrumentsCreateManyArgs} args - Arguments to create many Instruments.
     * @example
     * // Create many Instruments
     * const instruments = await prisma.instruments.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends InstrumentsCreateManyArgs>(args?: SelectSubset<T, InstrumentsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Instruments and returns the data saved in the database.
     * @param {InstrumentsCreateManyAndReturnArgs} args - Arguments to create many Instruments.
     * @example
     * // Create many Instruments
     * const instruments = await prisma.instruments.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Instruments and only return the `EXCH_ID`
     * const instrumentsWithEXCH_IDOnly = await prisma.instruments.createManyAndReturn({
     *   select: { EXCH_ID: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends InstrumentsCreateManyAndReturnArgs>(args?: SelectSubset<T, InstrumentsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Instruments.
     * @param {InstrumentsDeleteArgs} args - Arguments to delete one Instruments.
     * @example
     * // Delete one Instruments
     * const Instruments = await prisma.instruments.delete({
     *   where: {
     *     // ... filter to delete one Instruments
     *   }
     * })
     * 
     */
    delete<T extends InstrumentsDeleteArgs>(args: SelectSubset<T, InstrumentsDeleteArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Instruments.
     * @param {InstrumentsUpdateArgs} args - Arguments to update one Instruments.
     * @example
     * // Update one Instruments
     * const instruments = await prisma.instruments.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends InstrumentsUpdateArgs>(args: SelectSubset<T, InstrumentsUpdateArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Instruments.
     * @param {InstrumentsDeleteManyArgs} args - Arguments to filter Instruments to delete.
     * @example
     * // Delete a few Instruments
     * const { count } = await prisma.instruments.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends InstrumentsDeleteManyArgs>(args?: SelectSubset<T, InstrumentsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Instruments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Instruments
     * const instruments = await prisma.instruments.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends InstrumentsUpdateManyArgs>(args: SelectSubset<T, InstrumentsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Instruments and returns the data updated in the database.
     * @param {InstrumentsUpdateManyAndReturnArgs} args - Arguments to update many Instruments.
     * @example
     * // Update many Instruments
     * const instruments = await prisma.instruments.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Instruments and only return the `EXCH_ID`
     * const instrumentsWithEXCH_IDOnly = await prisma.instruments.updateManyAndReturn({
     *   select: { EXCH_ID: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends InstrumentsUpdateManyAndReturnArgs>(args: SelectSubset<T, InstrumentsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Instruments.
     * @param {InstrumentsUpsertArgs} args - Arguments to update or create a Instruments.
     * @example
     * // Update or create a Instruments
     * const instruments = await prisma.instruments.upsert({
     *   create: {
     *     // ... data to create a Instruments
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Instruments we want to update
     *   }
     * })
     */
    upsert<T extends InstrumentsUpsertArgs>(args: SelectSubset<T, InstrumentsUpsertArgs<ExtArgs>>): Prisma__InstrumentsClient<$Result.GetResult<Prisma.$InstrumentsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Instruments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsCountArgs} args - Arguments to filter Instruments to count.
     * @example
     * // Count the number of Instruments
     * const count = await prisma.instruments.count({
     *   where: {
     *     // ... the filter for the Instruments we want to count
     *   }
     * })
    **/
    count<T extends InstrumentsCountArgs>(
      args?: Subset<T, InstrumentsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], InstrumentsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Instruments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends InstrumentsAggregateArgs>(args: Subset<T, InstrumentsAggregateArgs>): Prisma.PrismaPromise<GetInstrumentsAggregateType<T>>

    /**
     * Group by Instruments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {InstrumentsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends InstrumentsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: InstrumentsGroupByArgs['orderBy'] }
        : { orderBy?: InstrumentsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, InstrumentsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetInstrumentsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Instruments model
   */
  readonly fields: InstrumentsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Instruments.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__InstrumentsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Instruments model
   */
  interface InstrumentsFieldRefs {
    readonly EXCH_ID: FieldRef<"Instruments", 'String'>
    readonly SEGMENT: FieldRef<"Instruments", 'String'>
    readonly SECURITY_ID: FieldRef<"Instruments", 'String'>
    readonly ISIN: FieldRef<"Instruments", 'String'>
    readonly INSTRUMENT: FieldRef<"Instruments", 'String'>
    readonly UNDERLYING_SECURITY_ID: FieldRef<"Instruments", 'String'>
    readonly UNDERLYING_SYMBOL: FieldRef<"Instruments", 'String'>
    readonly SYMBOL_NAME: FieldRef<"Instruments", 'String'>
    readonly DISPLAY_NAME: FieldRef<"Instruments", 'String'>
    readonly INSTRUMENT_TYPE: FieldRef<"Instruments", 'String'>
    readonly STRIKE_PRICE: FieldRef<"Instruments", 'String'>
    readonly OPTION_TYPE: FieldRef<"Instruments", 'String'>
    readonly SM_EXPIRY_DATE: FieldRef<"Instruments", 'String'>
    readonly LOT_SIZE: FieldRef<"Instruments", 'String'>
    readonly TICK_SIZE: FieldRef<"Instruments", 'String'>
    readonly MULTIPLIER: FieldRef<"Instruments", 'String'>
    readonly FREEZE_QTY: FieldRef<"Instruments", 'String'>
    readonly WARNING_QTY: FieldRef<"Instruments", 'String'>
    readonly DAILY_PRICE_BAND_LOWER: FieldRef<"Instruments", 'String'>
    readonly DAILY_PRICE_BAND_UPPER: FieldRef<"Instruments", 'String'>
    readonly TRADE_PRICE_BAND_LOWER: FieldRef<"Instruments", 'String'>
    readonly TRADE_PRICE_BAND_UPPER: FieldRef<"Instruments", 'String'>
    readonly MTF_LEVERAGE: FieldRef<"Instruments", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Instruments findUnique
   */
  export type InstrumentsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter, which Instruments to fetch.
     */
    where: InstrumentsWhereUniqueInput
  }

  /**
   * Instruments findUniqueOrThrow
   */
  export type InstrumentsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter, which Instruments to fetch.
     */
    where: InstrumentsWhereUniqueInput
  }

  /**
   * Instruments findFirst
   */
  export type InstrumentsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter, which Instruments to fetch.
     */
    where?: InstrumentsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Instruments to fetch.
     */
    orderBy?: InstrumentsOrderByWithRelationInput | InstrumentsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Instruments.
     */
    cursor?: InstrumentsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Instruments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Instruments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Instruments.
     */
    distinct?: InstrumentsScalarFieldEnum | InstrumentsScalarFieldEnum[]
  }

  /**
   * Instruments findFirstOrThrow
   */
  export type InstrumentsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter, which Instruments to fetch.
     */
    where?: InstrumentsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Instruments to fetch.
     */
    orderBy?: InstrumentsOrderByWithRelationInput | InstrumentsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Instruments.
     */
    cursor?: InstrumentsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Instruments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Instruments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Instruments.
     */
    distinct?: InstrumentsScalarFieldEnum | InstrumentsScalarFieldEnum[]
  }

  /**
   * Instruments findMany
   */
  export type InstrumentsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter, which Instruments to fetch.
     */
    where?: InstrumentsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Instruments to fetch.
     */
    orderBy?: InstrumentsOrderByWithRelationInput | InstrumentsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Instruments.
     */
    cursor?: InstrumentsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Instruments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Instruments.
     */
    skip?: number
    distinct?: InstrumentsScalarFieldEnum | InstrumentsScalarFieldEnum[]
  }

  /**
   * Instruments create
   */
  export type InstrumentsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * The data needed to create a Instruments.
     */
    data: XOR<InstrumentsCreateInput, InstrumentsUncheckedCreateInput>
  }

  /**
   * Instruments createMany
   */
  export type InstrumentsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Instruments.
     */
    data: InstrumentsCreateManyInput | InstrumentsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Instruments createManyAndReturn
   */
  export type InstrumentsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * The data used to create many Instruments.
     */
    data: InstrumentsCreateManyInput | InstrumentsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Instruments update
   */
  export type InstrumentsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * The data needed to update a Instruments.
     */
    data: XOR<InstrumentsUpdateInput, InstrumentsUncheckedUpdateInput>
    /**
     * Choose, which Instruments to update.
     */
    where: InstrumentsWhereUniqueInput
  }

  /**
   * Instruments updateMany
   */
  export type InstrumentsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Instruments.
     */
    data: XOR<InstrumentsUpdateManyMutationInput, InstrumentsUncheckedUpdateManyInput>
    /**
     * Filter which Instruments to update
     */
    where?: InstrumentsWhereInput
    /**
     * Limit how many Instruments to update.
     */
    limit?: number
  }

  /**
   * Instruments updateManyAndReturn
   */
  export type InstrumentsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * The data used to update Instruments.
     */
    data: XOR<InstrumentsUpdateManyMutationInput, InstrumentsUncheckedUpdateManyInput>
    /**
     * Filter which Instruments to update
     */
    where?: InstrumentsWhereInput
    /**
     * Limit how many Instruments to update.
     */
    limit?: number
  }

  /**
   * Instruments upsert
   */
  export type InstrumentsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * The filter to search for the Instruments to update in case it exists.
     */
    where: InstrumentsWhereUniqueInput
    /**
     * In case the Instruments found by the `where` argument doesn't exist, create a new Instruments with this data.
     */
    create: XOR<InstrumentsCreateInput, InstrumentsUncheckedCreateInput>
    /**
     * In case the Instruments was found with the provided `where` argument, update it with this data.
     */
    update: XOR<InstrumentsUpdateInput, InstrumentsUncheckedUpdateInput>
  }

  /**
   * Instruments delete
   */
  export type InstrumentsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
    /**
     * Filter which Instruments to delete.
     */
    where: InstrumentsWhereUniqueInput
  }

  /**
   * Instruments deleteMany
   */
  export type InstrumentsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Instruments to delete
     */
    where?: InstrumentsWhereInput
    /**
     * Limit how many Instruments to delete.
     */
    limit?: number
  }

  /**
   * Instruments without action
   */
  export type InstrumentsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Instruments
     */
    select?: InstrumentsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Instruments
     */
    omit?: InstrumentsOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const InstrumentsScalarFieldEnum: {
    EXCH_ID: 'EXCH_ID',
    SEGMENT: 'SEGMENT',
    SECURITY_ID: 'SECURITY_ID',
    ISIN: 'ISIN',
    INSTRUMENT: 'INSTRUMENT',
    UNDERLYING_SECURITY_ID: 'UNDERLYING_SECURITY_ID',
    UNDERLYING_SYMBOL: 'UNDERLYING_SYMBOL',
    SYMBOL_NAME: 'SYMBOL_NAME',
    DISPLAY_NAME: 'DISPLAY_NAME',
    INSTRUMENT_TYPE: 'INSTRUMENT_TYPE',
    STRIKE_PRICE: 'STRIKE_PRICE',
    OPTION_TYPE: 'OPTION_TYPE',
    SM_EXPIRY_DATE: 'SM_EXPIRY_DATE',
    LOT_SIZE: 'LOT_SIZE',
    TICK_SIZE: 'TICK_SIZE',
    MULTIPLIER: 'MULTIPLIER',
    FREEZE_QTY: 'FREEZE_QTY',
    WARNING_QTY: 'WARNING_QTY',
    DAILY_PRICE_BAND_LOWER: 'DAILY_PRICE_BAND_LOWER',
    DAILY_PRICE_BAND_UPPER: 'DAILY_PRICE_BAND_UPPER',
    TRADE_PRICE_BAND_LOWER: 'TRADE_PRICE_BAND_LOWER',
    TRADE_PRICE_BAND_UPPER: 'TRADE_PRICE_BAND_UPPER',
    MTF_LEVERAGE: 'MTF_LEVERAGE'
  };

  export type InstrumentsScalarFieldEnum = (typeof InstrumentsScalarFieldEnum)[keyof typeof InstrumentsScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type InstrumentsWhereInput = {
    AND?: InstrumentsWhereInput | InstrumentsWhereInput[]
    OR?: InstrumentsWhereInput[]
    NOT?: InstrumentsWhereInput | InstrumentsWhereInput[]
    EXCH_ID?: StringFilter<"Instruments"> | string
    SEGMENT?: StringFilter<"Instruments"> | string
    SECURITY_ID?: StringFilter<"Instruments"> | string
    ISIN?: StringNullableFilter<"Instruments"> | string | null
    INSTRUMENT?: StringFilter<"Instruments"> | string
    UNDERLYING_SECURITY_ID?: StringNullableFilter<"Instruments"> | string | null
    UNDERLYING_SYMBOL?: StringNullableFilter<"Instruments"> | string | null
    SYMBOL_NAME?: StringFilter<"Instruments"> | string
    DISPLAY_NAME?: StringNullableFilter<"Instruments"> | string | null
    INSTRUMENT_TYPE?: StringNullableFilter<"Instruments"> | string | null
    STRIKE_PRICE?: StringNullableFilter<"Instruments"> | string | null
    OPTION_TYPE?: StringNullableFilter<"Instruments"> | string | null
    SM_EXPIRY_DATE?: StringNullableFilter<"Instruments"> | string | null
    LOT_SIZE?: StringNullableFilter<"Instruments"> | string | null
    TICK_SIZE?: StringNullableFilter<"Instruments"> | string | null
    MULTIPLIER?: StringNullableFilter<"Instruments"> | string | null
    FREEZE_QTY?: StringNullableFilter<"Instruments"> | string | null
    WARNING_QTY?: StringNullableFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_LOWER?: StringNullableFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_UPPER?: StringNullableFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_LOWER?: StringNullableFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_UPPER?: StringNullableFilter<"Instruments"> | string | null
    MTF_LEVERAGE?: StringNullableFilter<"Instruments"> | string | null
  }

  export type InstrumentsOrderByWithRelationInput = {
    EXCH_ID?: SortOrder
    SEGMENT?: SortOrder
    SECURITY_ID?: SortOrder
    ISIN?: SortOrderInput | SortOrder
    INSTRUMENT?: SortOrder
    UNDERLYING_SECURITY_ID?: SortOrderInput | SortOrder
    UNDERLYING_SYMBOL?: SortOrderInput | SortOrder
    SYMBOL_NAME?: SortOrder
    DISPLAY_NAME?: SortOrderInput | SortOrder
    INSTRUMENT_TYPE?: SortOrderInput | SortOrder
    STRIKE_PRICE?: SortOrderInput | SortOrder
    OPTION_TYPE?: SortOrderInput | SortOrder
    SM_EXPIRY_DATE?: SortOrderInput | SortOrder
    LOT_SIZE?: SortOrderInput | SortOrder
    TICK_SIZE?: SortOrderInput | SortOrder
    MULTIPLIER?: SortOrderInput | SortOrder
    FREEZE_QTY?: SortOrderInput | SortOrder
    WARNING_QTY?: SortOrderInput | SortOrder
    DAILY_PRICE_BAND_LOWER?: SortOrderInput | SortOrder
    DAILY_PRICE_BAND_UPPER?: SortOrderInput | SortOrder
    TRADE_PRICE_BAND_LOWER?: SortOrderInput | SortOrder
    TRADE_PRICE_BAND_UPPER?: SortOrderInput | SortOrder
    MTF_LEVERAGE?: SortOrderInput | SortOrder
  }

  export type InstrumentsWhereUniqueInput = Prisma.AtLeast<{
    SECURITY_ID?: string
    AND?: InstrumentsWhereInput | InstrumentsWhereInput[]
    OR?: InstrumentsWhereInput[]
    NOT?: InstrumentsWhereInput | InstrumentsWhereInput[]
    EXCH_ID?: StringFilter<"Instruments"> | string
    SEGMENT?: StringFilter<"Instruments"> | string
    ISIN?: StringNullableFilter<"Instruments"> | string | null
    INSTRUMENT?: StringFilter<"Instruments"> | string
    UNDERLYING_SECURITY_ID?: StringNullableFilter<"Instruments"> | string | null
    UNDERLYING_SYMBOL?: StringNullableFilter<"Instruments"> | string | null
    SYMBOL_NAME?: StringFilter<"Instruments"> | string
    DISPLAY_NAME?: StringNullableFilter<"Instruments"> | string | null
    INSTRUMENT_TYPE?: StringNullableFilter<"Instruments"> | string | null
    STRIKE_PRICE?: StringNullableFilter<"Instruments"> | string | null
    OPTION_TYPE?: StringNullableFilter<"Instruments"> | string | null
    SM_EXPIRY_DATE?: StringNullableFilter<"Instruments"> | string | null
    LOT_SIZE?: StringNullableFilter<"Instruments"> | string | null
    TICK_SIZE?: StringNullableFilter<"Instruments"> | string | null
    MULTIPLIER?: StringNullableFilter<"Instruments"> | string | null
    FREEZE_QTY?: StringNullableFilter<"Instruments"> | string | null
    WARNING_QTY?: StringNullableFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_LOWER?: StringNullableFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_UPPER?: StringNullableFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_LOWER?: StringNullableFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_UPPER?: StringNullableFilter<"Instruments"> | string | null
    MTF_LEVERAGE?: StringNullableFilter<"Instruments"> | string | null
  }, "SECURITY_ID">

  export type InstrumentsOrderByWithAggregationInput = {
    EXCH_ID?: SortOrder
    SEGMENT?: SortOrder
    SECURITY_ID?: SortOrder
    ISIN?: SortOrderInput | SortOrder
    INSTRUMENT?: SortOrder
    UNDERLYING_SECURITY_ID?: SortOrderInput | SortOrder
    UNDERLYING_SYMBOL?: SortOrderInput | SortOrder
    SYMBOL_NAME?: SortOrder
    DISPLAY_NAME?: SortOrderInput | SortOrder
    INSTRUMENT_TYPE?: SortOrderInput | SortOrder
    STRIKE_PRICE?: SortOrderInput | SortOrder
    OPTION_TYPE?: SortOrderInput | SortOrder
    SM_EXPIRY_DATE?: SortOrderInput | SortOrder
    LOT_SIZE?: SortOrderInput | SortOrder
    TICK_SIZE?: SortOrderInput | SortOrder
    MULTIPLIER?: SortOrderInput | SortOrder
    FREEZE_QTY?: SortOrderInput | SortOrder
    WARNING_QTY?: SortOrderInput | SortOrder
    DAILY_PRICE_BAND_LOWER?: SortOrderInput | SortOrder
    DAILY_PRICE_BAND_UPPER?: SortOrderInput | SortOrder
    TRADE_PRICE_BAND_LOWER?: SortOrderInput | SortOrder
    TRADE_PRICE_BAND_UPPER?: SortOrderInput | SortOrder
    MTF_LEVERAGE?: SortOrderInput | SortOrder
    _count?: InstrumentsCountOrderByAggregateInput
    _max?: InstrumentsMaxOrderByAggregateInput
    _min?: InstrumentsMinOrderByAggregateInput
  }

  export type InstrumentsScalarWhereWithAggregatesInput = {
    AND?: InstrumentsScalarWhereWithAggregatesInput | InstrumentsScalarWhereWithAggregatesInput[]
    OR?: InstrumentsScalarWhereWithAggregatesInput[]
    NOT?: InstrumentsScalarWhereWithAggregatesInput | InstrumentsScalarWhereWithAggregatesInput[]
    EXCH_ID?: StringWithAggregatesFilter<"Instruments"> | string
    SEGMENT?: StringWithAggregatesFilter<"Instruments"> | string
    SECURITY_ID?: StringWithAggregatesFilter<"Instruments"> | string
    ISIN?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    INSTRUMENT?: StringWithAggregatesFilter<"Instruments"> | string
    UNDERLYING_SECURITY_ID?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    UNDERLYING_SYMBOL?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    SYMBOL_NAME?: StringWithAggregatesFilter<"Instruments"> | string
    DISPLAY_NAME?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    INSTRUMENT_TYPE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    STRIKE_PRICE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    OPTION_TYPE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    SM_EXPIRY_DATE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    LOT_SIZE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    TICK_SIZE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    MULTIPLIER?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    FREEZE_QTY?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    WARNING_QTY?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_LOWER?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    DAILY_PRICE_BAND_UPPER?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_LOWER?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    TRADE_PRICE_BAND_UPPER?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
    MTF_LEVERAGE?: StringNullableWithAggregatesFilter<"Instruments"> | string | null
  }

  export type InstrumentsCreateInput = {
    EXCH_ID: string
    SEGMENT: string
    SECURITY_ID: string
    ISIN?: string | null
    INSTRUMENT: string
    UNDERLYING_SECURITY_ID?: string | null
    UNDERLYING_SYMBOL?: string | null
    SYMBOL_NAME: string
    DISPLAY_NAME?: string | null
    INSTRUMENT_TYPE?: string | null
    STRIKE_PRICE?: string | null
    OPTION_TYPE?: string | null
    SM_EXPIRY_DATE?: string | null
    LOT_SIZE?: string | null
    TICK_SIZE?: string | null
    MULTIPLIER?: string | null
    FREEZE_QTY?: string | null
    WARNING_QTY?: string | null
    DAILY_PRICE_BAND_LOWER?: string | null
    DAILY_PRICE_BAND_UPPER?: string | null
    TRADE_PRICE_BAND_LOWER?: string | null
    TRADE_PRICE_BAND_UPPER?: string | null
    MTF_LEVERAGE?: string | null
  }

  export type InstrumentsUncheckedCreateInput = {
    EXCH_ID: string
    SEGMENT: string
    SECURITY_ID: string
    ISIN?: string | null
    INSTRUMENT: string
    UNDERLYING_SECURITY_ID?: string | null
    UNDERLYING_SYMBOL?: string | null
    SYMBOL_NAME: string
    DISPLAY_NAME?: string | null
    INSTRUMENT_TYPE?: string | null
    STRIKE_PRICE?: string | null
    OPTION_TYPE?: string | null
    SM_EXPIRY_DATE?: string | null
    LOT_SIZE?: string | null
    TICK_SIZE?: string | null
    MULTIPLIER?: string | null
    FREEZE_QTY?: string | null
    WARNING_QTY?: string | null
    DAILY_PRICE_BAND_LOWER?: string | null
    DAILY_PRICE_BAND_UPPER?: string | null
    TRADE_PRICE_BAND_LOWER?: string | null
    TRADE_PRICE_BAND_UPPER?: string | null
    MTF_LEVERAGE?: string | null
  }

  export type InstrumentsUpdateInput = {
    EXCH_ID?: StringFieldUpdateOperationsInput | string
    SEGMENT?: StringFieldUpdateOperationsInput | string
    SECURITY_ID?: StringFieldUpdateOperationsInput | string
    ISIN?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT?: StringFieldUpdateOperationsInput | string
    UNDERLYING_SECURITY_ID?: NullableStringFieldUpdateOperationsInput | string | null
    UNDERLYING_SYMBOL?: NullableStringFieldUpdateOperationsInput | string | null
    SYMBOL_NAME?: StringFieldUpdateOperationsInput | string
    DISPLAY_NAME?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    STRIKE_PRICE?: NullableStringFieldUpdateOperationsInput | string | null
    OPTION_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    SM_EXPIRY_DATE?: NullableStringFieldUpdateOperationsInput | string | null
    LOT_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    TICK_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    MULTIPLIER?: NullableStringFieldUpdateOperationsInput | string | null
    FREEZE_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    WARNING_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    MTF_LEVERAGE?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InstrumentsUncheckedUpdateInput = {
    EXCH_ID?: StringFieldUpdateOperationsInput | string
    SEGMENT?: StringFieldUpdateOperationsInput | string
    SECURITY_ID?: StringFieldUpdateOperationsInput | string
    ISIN?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT?: StringFieldUpdateOperationsInput | string
    UNDERLYING_SECURITY_ID?: NullableStringFieldUpdateOperationsInput | string | null
    UNDERLYING_SYMBOL?: NullableStringFieldUpdateOperationsInput | string | null
    SYMBOL_NAME?: StringFieldUpdateOperationsInput | string
    DISPLAY_NAME?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    STRIKE_PRICE?: NullableStringFieldUpdateOperationsInput | string | null
    OPTION_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    SM_EXPIRY_DATE?: NullableStringFieldUpdateOperationsInput | string | null
    LOT_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    TICK_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    MULTIPLIER?: NullableStringFieldUpdateOperationsInput | string | null
    FREEZE_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    WARNING_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    MTF_LEVERAGE?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InstrumentsCreateManyInput = {
    EXCH_ID: string
    SEGMENT: string
    SECURITY_ID: string
    ISIN?: string | null
    INSTRUMENT: string
    UNDERLYING_SECURITY_ID?: string | null
    UNDERLYING_SYMBOL?: string | null
    SYMBOL_NAME: string
    DISPLAY_NAME?: string | null
    INSTRUMENT_TYPE?: string | null
    STRIKE_PRICE?: string | null
    OPTION_TYPE?: string | null
    SM_EXPIRY_DATE?: string | null
    LOT_SIZE?: string | null
    TICK_SIZE?: string | null
    MULTIPLIER?: string | null
    FREEZE_QTY?: string | null
    WARNING_QTY?: string | null
    DAILY_PRICE_BAND_LOWER?: string | null
    DAILY_PRICE_BAND_UPPER?: string | null
    TRADE_PRICE_BAND_LOWER?: string | null
    TRADE_PRICE_BAND_UPPER?: string | null
    MTF_LEVERAGE?: string | null
  }

  export type InstrumentsUpdateManyMutationInput = {
    EXCH_ID?: StringFieldUpdateOperationsInput | string
    SEGMENT?: StringFieldUpdateOperationsInput | string
    SECURITY_ID?: StringFieldUpdateOperationsInput | string
    ISIN?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT?: StringFieldUpdateOperationsInput | string
    UNDERLYING_SECURITY_ID?: NullableStringFieldUpdateOperationsInput | string | null
    UNDERLYING_SYMBOL?: NullableStringFieldUpdateOperationsInput | string | null
    SYMBOL_NAME?: StringFieldUpdateOperationsInput | string
    DISPLAY_NAME?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    STRIKE_PRICE?: NullableStringFieldUpdateOperationsInput | string | null
    OPTION_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    SM_EXPIRY_DATE?: NullableStringFieldUpdateOperationsInput | string | null
    LOT_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    TICK_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    MULTIPLIER?: NullableStringFieldUpdateOperationsInput | string | null
    FREEZE_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    WARNING_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    MTF_LEVERAGE?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type InstrumentsUncheckedUpdateManyInput = {
    EXCH_ID?: StringFieldUpdateOperationsInput | string
    SEGMENT?: StringFieldUpdateOperationsInput | string
    SECURITY_ID?: StringFieldUpdateOperationsInput | string
    ISIN?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT?: StringFieldUpdateOperationsInput | string
    UNDERLYING_SECURITY_ID?: NullableStringFieldUpdateOperationsInput | string | null
    UNDERLYING_SYMBOL?: NullableStringFieldUpdateOperationsInput | string | null
    SYMBOL_NAME?: StringFieldUpdateOperationsInput | string
    DISPLAY_NAME?: NullableStringFieldUpdateOperationsInput | string | null
    INSTRUMENT_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    STRIKE_PRICE?: NullableStringFieldUpdateOperationsInput | string | null
    OPTION_TYPE?: NullableStringFieldUpdateOperationsInput | string | null
    SM_EXPIRY_DATE?: NullableStringFieldUpdateOperationsInput | string | null
    LOT_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    TICK_SIZE?: NullableStringFieldUpdateOperationsInput | string | null
    MULTIPLIER?: NullableStringFieldUpdateOperationsInput | string | null
    FREEZE_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    WARNING_QTY?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    DAILY_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_LOWER?: NullableStringFieldUpdateOperationsInput | string | null
    TRADE_PRICE_BAND_UPPER?: NullableStringFieldUpdateOperationsInput | string | null
    MTF_LEVERAGE?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type InstrumentsCountOrderByAggregateInput = {
    EXCH_ID?: SortOrder
    SEGMENT?: SortOrder
    SECURITY_ID?: SortOrder
    ISIN?: SortOrder
    INSTRUMENT?: SortOrder
    UNDERLYING_SECURITY_ID?: SortOrder
    UNDERLYING_SYMBOL?: SortOrder
    SYMBOL_NAME?: SortOrder
    DISPLAY_NAME?: SortOrder
    INSTRUMENT_TYPE?: SortOrder
    STRIKE_PRICE?: SortOrder
    OPTION_TYPE?: SortOrder
    SM_EXPIRY_DATE?: SortOrder
    LOT_SIZE?: SortOrder
    TICK_SIZE?: SortOrder
    MULTIPLIER?: SortOrder
    FREEZE_QTY?: SortOrder
    WARNING_QTY?: SortOrder
    DAILY_PRICE_BAND_LOWER?: SortOrder
    DAILY_PRICE_BAND_UPPER?: SortOrder
    TRADE_PRICE_BAND_LOWER?: SortOrder
    TRADE_PRICE_BAND_UPPER?: SortOrder
    MTF_LEVERAGE?: SortOrder
  }

  export type InstrumentsMaxOrderByAggregateInput = {
    EXCH_ID?: SortOrder
    SEGMENT?: SortOrder
    SECURITY_ID?: SortOrder
    ISIN?: SortOrder
    INSTRUMENT?: SortOrder
    UNDERLYING_SECURITY_ID?: SortOrder
    UNDERLYING_SYMBOL?: SortOrder
    SYMBOL_NAME?: SortOrder
    DISPLAY_NAME?: SortOrder
    INSTRUMENT_TYPE?: SortOrder
    STRIKE_PRICE?: SortOrder
    OPTION_TYPE?: SortOrder
    SM_EXPIRY_DATE?: SortOrder
    LOT_SIZE?: SortOrder
    TICK_SIZE?: SortOrder
    MULTIPLIER?: SortOrder
    FREEZE_QTY?: SortOrder
    WARNING_QTY?: SortOrder
    DAILY_PRICE_BAND_LOWER?: SortOrder
    DAILY_PRICE_BAND_UPPER?: SortOrder
    TRADE_PRICE_BAND_LOWER?: SortOrder
    TRADE_PRICE_BAND_UPPER?: SortOrder
    MTF_LEVERAGE?: SortOrder
  }

  export type InstrumentsMinOrderByAggregateInput = {
    EXCH_ID?: SortOrder
    SEGMENT?: SortOrder
    SECURITY_ID?: SortOrder
    ISIN?: SortOrder
    INSTRUMENT?: SortOrder
    UNDERLYING_SECURITY_ID?: SortOrder
    UNDERLYING_SYMBOL?: SortOrder
    SYMBOL_NAME?: SortOrder
    DISPLAY_NAME?: SortOrder
    INSTRUMENT_TYPE?: SortOrder
    STRIKE_PRICE?: SortOrder
    OPTION_TYPE?: SortOrder
    SM_EXPIRY_DATE?: SortOrder
    LOT_SIZE?: SortOrder
    TICK_SIZE?: SortOrder
    MULTIPLIER?: SortOrder
    FREEZE_QTY?: SortOrder
    WARNING_QTY?: SortOrder
    DAILY_PRICE_BAND_LOWER?: SortOrder
    DAILY_PRICE_BAND_UPPER?: SortOrder
    TRADE_PRICE_BAND_LOWER?: SortOrder
    TRADE_PRICE_BAND_UPPER?: SortOrder
    MTF_LEVERAGE?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}