"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        console.log(\"\\uD83D\\uDCCA Total market data entries:\", marketData.size);\n        // Debug: Show sample market data entries\n        const sampleEntries = Array.from(marketData.entries()).slice(0, 10);\n        console.log(\"\\uD83D\\uDCCB Sample market data entries:\", sampleEntries.map((param)=>{\n            let [id, data] = param;\n            return {\n                securityId: id,\n                symbol: data.symbol,\n                expiryDate: data.expiryDate,\n                optionType: data.optionType,\n                strikePrice: data.strikePrice\n            };\n        }));\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        console.log(\"\\uD83C\\uDFAF Generated strikes:\", strikes);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        console.log(\"\\uD83D\\uDCC8 Option chain rows built:\", rows.length, \"rows\");\n        console.log(\"✅ Found options:\", rows.filter((r)=>r.call || r.put).length, \"strikes with data\");\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n            console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), availableOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_call7, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6, _row_put7;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-500\"),\n                                        children: ((_row_call7 = row.call) === null || _row_call7 === void 0 ? void 0 : _row_call7.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-500\"),\n                                        children: ((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change)),\n                                        children: ((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put7 = row.put) === null || _row_put7 === void 0 ? void 0 : _row_put7.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 528,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"3MYzdkz2i0N1g/MoBahr6e4MTpU=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09wdGlvbkNoYWluLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEM7QUFPN0IsU0FBU0UsWUFBWSxLQUFnQztRQUFoQyxFQUFFQyxVQUFVLEVBQW9CLEdBQWhDOztJQUNsQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0wsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQ00sZ0JBQWdCQyxrQkFBa0IsR0FBR1AsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUF5QjtJQUN2RSxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNjLGdCQUFnQkMsa0JBQWtCLEdBQUdmLCtDQUFRQSxDQUFTO0lBRTdELDZGQUE2RjtJQUM3RkMsZ0RBQVNBLENBQUM7UUFDUixvR0FBb0c7UUFDcEcsTUFBTWUsWUFBWWIsV0FBV2MsR0FBRyxDQUFDO1FBQ2pDLElBQUlELGFBQWFBLFVBQVVFLEdBQUcsR0FBRyxHQUFHO1lBQ2xDSCxrQkFBa0JDLFVBQVVFLEdBQUc7WUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxpREFBdUNKLFVBQVVFLEdBQUcsRUFBRTtZQUNsRTtRQUNGO1FBRUEsMkVBQTJFO1FBQzNFLEtBQUssTUFBTSxDQUFDRyxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUsSUFBSUgsS0FBS0ksTUFBTSxLQUFLLFdBQVdKLEtBQUtKLEdBQUcsR0FBRyxHQUFHO2dCQUMzQ0gsa0JBQWtCTyxLQUFLSixHQUFHO2dCQUMxQkMsUUFBUUMsR0FBRyxDQUFDLCtDQUFxQ0UsS0FBS0osR0FBRyxFQUFFLGtCQUFrQkcsWUFBWSxhQUFhQyxLQUFLSyxRQUFRO2dCQUNuSDtZQUNGO1FBQ0Y7UUFFQSx1RkFBdUY7UUFDdkYsSUFBSWIsbUJBQW1CLEdBQUc7WUFDeEIsTUFBTWMsWUFBWTtZQUNsQmIsa0JBQWtCYTtZQUNsQlQsUUFBUVUsSUFBSSxDQUFDLDBDQUEwQ0QsV0FBVztRQUNwRTtJQUNGLEdBQUc7UUFBQ3pCO0tBQVc7SUFFZix3Q0FBd0M7SUFDeENGLGdEQUFTQSxDQUFDO1FBQ1I2QjtJQUNGLEdBQUcsRUFBRTtJQUVMLDZDQUE2QztJQUM3QzdCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSUssa0JBQWtCUSxpQkFBaUIsR0FBRztZQUN4Q2lCO1FBQ0Y7SUFDRixHQUFHO1FBQUN6QjtRQUFnQlE7UUFBZ0JYO0tBQVc7SUFFL0MsTUFBTTJCLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0ZuQixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxNQUFNbUIsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0saUNBQXFELE9BQXBCSCxTQUFTSSxVQUFVO1lBQ3RFO1lBRUEsTUFBTUMsU0FBUyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2xDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJsQyxjQUFjZ0MsT0FBT2YsSUFBSTtnQkFDekIsMkJBQTJCO2dCQUMzQixJQUFJZSxPQUFPZixJQUFJLENBQUNrQixRQUFRLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUNuQ2xDLGtCQUFrQjhCLE9BQU9mLElBQUksQ0FBQ2tCLFFBQVEsQ0FBQyxFQUFFO2dCQUMzQztZQUNGLE9BQU87Z0JBQ0wsTUFBTSxJQUFJTCxNQUFNRSxPQUFPSyxPQUFPLElBQUk7WUFDcEM7UUFDRixFQUFFLE9BQU9DLEtBQUs7WUFDWnhCLFFBQVFQLEtBQUssQ0FBQyxnQ0FBZ0MrQjtZQUM5QzlCLFNBQVM4QixlQUFlUixRQUFRUSxJQUFJRCxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSL0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNb0IsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ3pCLGtCQUFrQlEsa0JBQWtCLEdBQUc7UUFFNUNLLFFBQVFDLEdBQUcsQ0FBQyxrREFBd0NkLGdCQUFnQixTQUFTUTtRQUM3RUssUUFBUUMsR0FBRyxDQUFDLDJDQUFpQ2pCLFdBQVd5QyxJQUFJO1FBRTVELHlDQUF5QztRQUN6QyxNQUFNQyxnQkFBZ0J0QixNQUFNQyxJQUFJLENBQUNyQixXQUFXc0IsT0FBTyxJQUFJcUIsS0FBSyxDQUFDLEdBQUc7UUFDaEUzQixRQUFRQyxHQUFHLENBQUMsNENBQWtDeUIsY0FBY0UsR0FBRyxDQUFDO2dCQUFDLENBQUNDLElBQUkxQixLQUFLO21CQUFNO2dCQUMvRUQsWUFBWTJCO2dCQUNadEIsUUFBUUosS0FBS0ksTUFBTTtnQkFDbkJ1QixZQUFZM0IsS0FBSzJCLFVBQVU7Z0JBQzNCQyxZQUFZNUIsS0FBSzRCLFVBQVU7Z0JBQzNCQyxhQUFhN0IsS0FBSzZCLFdBQVc7WUFDL0I7O1FBRUEsMkNBQTJDO1FBQzNDLE1BQU1DLFVBQVVDLGdCQUFnQnZDO1FBQ2hDSyxRQUFRQyxHQUFHLENBQUMsbUNBQXlCZ0M7UUFFckMsMEJBQTBCO1FBQzFCLE1BQU1FLE9BQXlCRixRQUFRTCxHQUFHLENBQUNRLENBQUFBO1lBQ3pDLE1BQU1DLFdBQVdDLGVBQWVGLFFBQVE7WUFDeEMsTUFBTUcsVUFBVUQsZUFBZUYsUUFBUTtZQUV2QyxPQUFPO2dCQUNMSixhQUFhSTtnQkFDYkksTUFBTUg7Z0JBQ05JLEtBQUtGO1lBQ1A7UUFDRjtRQUVBdkMsUUFBUUMsR0FBRyxDQUFDLHlDQUErQmtDLEtBQUtiLE1BQU0sRUFBRTtRQUN4RHRCLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JrQyxLQUFLTyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVILElBQUksSUFBSUcsRUFBRUYsR0FBRyxFQUFFbkIsTUFBTSxFQUFFO1FBRTFFaEMsZUFBZTtZQUNic0QsWUFBWTtZQUNaQyxXQUFXbEQ7WUFDWG1ELFFBQVEzRDtZQUNSZ0Q7WUFDQVksV0FBV0MsS0FBS0MsR0FBRztRQUNyQjtJQUNGO0lBRUEsTUFBTWYsa0JBQWtCLENBQUNXO1FBQ3ZCLDRFQUE0RTtRQUM1RSxNQUFNSyxtQkFBbUIsSUFBSUM7UUFFN0IsS0FBSyxNQUFNLENBQUNqRCxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUsSUFBSUgsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDLFlBQ3JCakQsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDQyxzQkFBc0JsRSxrQkFBa0I7Z0JBRS9ELDREQUE0RDtnQkFDNUQsTUFBTW1FLGNBQWNuRCxLQUFLSSxNQUFNLENBQUNnRCxLQUFLLENBQUM7Z0JBQ3RDLElBQUlELFlBQVloQyxNQUFNLElBQUksR0FBRztvQkFDM0IsTUFBTWMsU0FBU29CLFdBQVdGLFdBQVcsQ0FBQyxFQUFFO29CQUN4QyxJQUFJLENBQUNHLE1BQU1yQixTQUFTO3dCQUNsQmMsaUJBQWlCUSxHQUFHLENBQUN0QjtvQkFDdkI7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSWMsaUJBQWlCekIsSUFBSSxLQUFLLEdBQUc7WUFDL0IsK0NBQStDO1lBQy9DLE1BQU1RLFVBQW9CLEVBQUU7WUFDNUIsTUFBTTBCLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ2hCLFlBQVksTUFBTTtZQUNoRCxJQUFLLElBQUlpQixJQUFJLENBQUMsSUFBSUEsS0FBSyxJQUFJQSxJQUFLO2dCQUM5QjdCLFFBQVE4QixJQUFJLENBQUNKLGFBQWNHLElBQUk7WUFDakM7WUFDQSxPQUFPN0IsUUFBUStCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUNwQztRQUVBLDBCQUEwQjtRQUMxQixNQUFNQyxhQUFhL0QsTUFBTUMsSUFBSSxDQUFDNkMsa0JBQWtCYyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7UUFFbkUsMENBQTBDO1FBQzFDLE1BQU1FLFlBQVlELFdBQVdFLE1BQU0sQ0FBQyxDQUFDQyxNQUFNQyxPQUN6Q1gsS0FBS1ksR0FBRyxDQUFDRCxPQUFPMUIsYUFBYWUsS0FBS1ksR0FBRyxDQUFDRixPQUFPekIsYUFBYTBCLE9BQU9EO1FBR25FdEUsUUFBUUMsR0FBRyxDQUFDLGdEQUEwRDRDLE9BQXBCdUIsV0FBVSxZQUFvQixPQUFWdkIsV0FBVTtRQUNoRjdDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBb0QsT0FBbEJrRSxXQUFXN0MsTUFBTTtRQUUvRCwrRUFBK0U7UUFDL0UsTUFBTW1ELFdBQVdOLFdBQVdPLE9BQU8sQ0FBQ047UUFDcEMsTUFBTU8sa0JBQWtCO2VBQ25CUixXQUFXeEMsS0FBSyxDQUFDaUMsS0FBS2dCLEdBQUcsQ0FBQyxHQUFHSCxXQUFXLEtBQUtBO1lBQ2hETDtlQUNHRCxXQUFXeEMsS0FBSyxDQUFDOEMsV0FBVyxHQUFHQSxXQUFXO1NBQzlDO1FBRUR6RSxRQUFRQyxHQUFHLENBQUMsdUJBQThDLE9BQXZCMEUsZ0JBQWdCckQsTUFBTSxFQUFDLHlCQUF1QnFEO1FBRWpGLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNckMsaUJBQWlCLENBQUNGLFFBQWdCTDtRQUN0QyxxRUFBcUU7UUFDckUsS0FBSyxNQUFNLENBQUM3QixZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUseURBQXlEO1lBQ3pELElBQUlILEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxhQUFjLDRDQUE0QztZQUMvRWpELEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxJQUFjckIsT0FBVkssUUFBTyxLQUFjLE9BQVhMLGdCQUNuQzVCLEtBQUsyQixVQUFVLEtBQUszQyxnQkFBZ0I7b0JBZ0IvQmdCLG9CQUFBQSxtQkFDQUEscUJBQUFBLG9CQUNHQSxxQkFBQUEsb0JBQ0FBLHFCQUFBQTtnQkFqQlZILFFBQVFDLEdBQUcsQ0FBQyxvQkFBa0NtQyxPQUFkTCxZQUFXLEtBQWM1QixPQUFYaUMsUUFBTyxNQUE0QmpDLE9BQXhCQSxLQUFLSSxNQUFNLEVBQUMsY0FBNEIsT0FBaEJKLEtBQUsyQixVQUFVLEVBQUM7Z0JBRWpHLE9BQU87b0JBQ0w1QjtvQkFDQUssUUFBUUosS0FBS0ksTUFBTTtvQkFDbkJDLFVBQVVMLEtBQUtLLFFBQVE7b0JBQ3ZCd0IsYUFBYUk7b0JBQ2JMO29CQUNBRCxZQUFZM0M7b0JBQ1pZLEtBQUtJLEtBQUtKLEdBQUcsSUFBSTtvQkFDakI4RSxRQUFRMUUsS0FBSzBFLE1BQU0sSUFBSTtvQkFDdkJDLGVBQWUzRSxLQUFLMkUsYUFBYSxJQUFJO29CQUNyQ0MsUUFBUTVFLEtBQUs0RSxNQUFNLElBQUk7b0JBQ3ZCQyxjQUFjN0UsS0FBSzZFLFlBQVk7b0JBQy9CQyxLQUFLOUUsRUFBQUEsb0JBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UseUNBQUFBLHFCQUFBQSxpQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSx5Q0FBQUEsbUJBQXVCZ0YsUUFBUSxLQUFJaEYsS0FBSzhFLEdBQUc7b0JBQ2hERyxLQUFLakYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCa0YsUUFBUSxLQUFJbEYsS0FBS2lGLEdBQUc7b0JBQ2hERSxRQUFRbkYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCbUYsTUFBTSxLQUFJbkYsS0FBS21GLE1BQU07b0JBQ3BEQyxRQUFRcEYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCb0YsTUFBTSxLQUFJcEYsS0FBS29GLE1BQU07b0JBQ3BEQyxNQUFNckYsS0FBS3FGLElBQUk7b0JBQ2ZDLEtBQUt0RixLQUFLc0YsR0FBRztvQkFDYkMsTUFBTXZGLEtBQUt1RixJQUFJO29CQUNmQyxPQUFPeEYsS0FBS3dGLEtBQUs7b0JBQ2pCNUMsV0FBVzVDLEtBQUs0QyxTQUFTO2dCQUMzQjtZQUNGO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsSUFBSTZDLElBQXlCLEVBQWU7WUFDMUM1RixRQUFRQyxHQUFHLENBQUMsd0JBQXNDbUMsT0FBZEwsWUFBVyxLQUF3QjVDLE9BQXJCaUQsUUFBTyxnQkFBNkIsT0FBZmpEO1lBRXZFLHVDQUF1QztZQUN2QyxNQUFNMEcsbUJBQW1CekYsTUFBTUMsSUFBSSxDQUFDckIsV0FBVzhHLE1BQU0sSUFDbERwRCxNQUFNLENBQUN2QyxDQUFBQSxPQUFRQSxLQUFLSSxNQUFNLENBQUM2QyxRQUFRLENBQUMsYUFBYWpELEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxJQUFlLE9BQVhyQixjQUMxRUosS0FBSyxDQUFDLEdBQUc7WUFFWjNCLFFBQVFDLEdBQUcsQ0FBQyxxQkFBZ0MsT0FBWDhCLFlBQVcsY0FBWThELGlCQUFpQmpFLEdBQUcsQ0FBQ21FLENBQUFBLE1BQzNFLEdBQTBCQSxPQUF2QkEsSUFBSXhGLE1BQU0sRUFBQyxjQUEyQixPQUFmd0YsSUFBSWpFLFVBQVUsRUFBQztRQUU3QztRQUVBLE9BQU87SUFDVDtJQUVBLE1BQU11Qix3QkFBd0IsQ0FBQ1A7UUFDN0IsK0RBQStEO1FBQy9ELE1BQU1rRCxPQUFPLElBQUloRCxLQUFLRjtRQUN0QixNQUFNbUQsU0FBUztZQUFDO1lBQU87WUFBTztZQUFPO1lBQU87WUFBTztZQUNwQztZQUFPO1lBQU87WUFBTztZQUFPO1lBQU87U0FBTTtRQUN4RCxPQUFPLEdBQTZCRCxPQUExQkMsTUFBTSxDQUFDRCxLQUFLRSxRQUFRLEdBQUcsRUFBc0IsT0FBbkJGLEtBQUtHLFdBQVc7SUFDdEQ7SUFFQSxNQUFNQyxjQUFjLENBQUNDO1FBQ25CLElBQUksQ0FBQ0EsU0FBU0EsU0FBUyxHQUFHLE9BQU87UUFDakMsT0FBTyxJQUFxQixPQUFqQkEsTUFBTUMsT0FBTyxDQUFDO0lBQzNCO0lBRUEsTUFBTUMsZUFBZSxDQUFDQztRQUNwQixJQUFJLENBQUNBLEtBQUssT0FBTztRQUNqQixPQUFPQSxJQUFJQyxjQUFjO0lBQzNCO0lBRUEsTUFBTUMsaUJBQWlCLENBQUM3QjtRQUN0QixJQUFJLENBQUNBLFFBQVEsT0FBTztRQUNwQixPQUFPQSxTQUFTLElBQUksbUJBQW1CQSxTQUFTLElBQUksaUJBQWlCO0lBQ3ZFO0lBRUEsSUFBSXRGLFNBQVM7UUFDWCxxQkFDRSw4REFBQ29IO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLElBQUluSCxPQUFPO1FBQ1QscUJBQ0UsOERBQUNrSDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0U7b0JBQUdGLFdBQVU7OEJBQTJCOzs7Ozs7OEJBQ3pDLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBNkJuSDs7Ozs7OzhCQUMxQyw4REFBQ3NIO29CQUNDQyxTQUFTckc7b0JBQ1RpRyxXQUFVOzhCQUNYOzs7Ozs7Ozs7Ozs7SUFLUDtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FHakQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0c7NENBQU9ILFdBQVU7c0RBQW9FOzs7Ozs7c0RBR3RGLDhEQUFDRzs0Q0FBT0gsV0FBVTtzREFBNkU7Ozs7OztzREFHL0YsOERBQUNHOzRDQUFPSCxXQUFVO3NEQUE2RTs7Ozs7O3NEQUcvRiw4REFBQ0c7NENBQU9ILFdBQVU7c0RBQTZFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT25HLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUF3Qjs7Ozs7OzhDQUd2Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBS04sV0FBVTtzREFBd0I7Ozs7OztzREFDeEMsOERBQUNNOzRDQUFLTixXQUFVO3NEQUNiUixZQUFZekc7Ozs7Ozt3Q0FFZEEsbUJBQW1CLHVCQUNsQiw4REFBQ3VIOzRDQUFLTixXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRakQzSCw0QkFDQyw4REFBQzBIO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1ozSCxXQUFXb0MsUUFBUSxDQUFDTSxLQUFLLENBQUMsR0FBRyxJQUFJQyxHQUFHLENBQUMsQ0FBQ2tCLFFBQVFxRTs0QkFDN0MsTUFBTW5CLE9BQU8sSUFBSWhELEtBQUtGOzRCQUN0QixNQUFNc0UsYUFBYXRFLFdBQVczRDs0QkFDOUIsTUFBTWtJLFVBQVVyQixLQUFLc0IsWUFBWSxPQUFPLElBQUl0RSxPQUFPc0UsWUFBWTs0QkFDL0QsTUFBTUMsZ0JBQWdCM0QsS0FBS1ksR0FBRyxDQUFDd0IsS0FBS3dCLE9BQU8sS0FBSyxJQUFJeEUsT0FBT3dFLE9BQU8sTUFBTSxJQUFJLEtBQUssS0FBSyxLQUFLOzRCQUUzRiwyQkFBMkI7NEJBQzNCLE1BQU1DLGNBQWMsSUFBSXpFLE9BQU9tRCxXQUFXOzRCQUMxQyxNQUFNdUIsYUFBYTFCLEtBQUtHLFdBQVc7NEJBQ25DLE1BQU13QixhQUFhM0IsS0FBSzRCLGtCQUFrQixDQUFDLFNBQVM7Z0NBQUVDLE9BQU87NEJBQVE7NEJBQ3JFLE1BQU1DLE1BQU05QixLQUFLK0IsT0FBTzs0QkFFeEIsMkNBQTJDOzRCQUMzQyxNQUFNQyxXQUFXTixlQUFlRDs0QkFFaEMscUJBQ0UsOERBQUNWO2dDQUVDQyxTQUFTLElBQU01SCxrQkFBa0IwRDtnQ0FDakM4RCxXQUFXLHdFQVFWLE9BUENRLGFBQ0kscUNBQ0FDLFVBQ0EsK0NBQ0FFLGdCQUNBLGdFQUNBOzBDQUdOLDRFQUFDWjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFDWmU7Z0RBQVlLLFlBQVksSUFBb0MsT0FBaENOLFdBQVdPLFFBQVEsR0FBR3RHLEtBQUssQ0FBQyxDQUFDOzs7Ozs7O3NEQUU1RCw4REFBQ2dGOzRDQUFJQyxXQUFVO3NEQUNaa0I7Ozs7Ozs7Ozs7OzsrQkFqQkFoRjs7Ozs7d0JBc0JYOzs7Ozs7b0JBaFhaLEtBb1hvQyxrQkFDeEIsOERBQUM2RDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUF5Qjt3Q0FBRS9JOzs7Ozs7OzhDQUN4Qyw4REFBQ3dIOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQW9CO3dDQUFHdkk7d0NBQWU7d0NBQUVBLG1CQUFtQixRQUFRLFdBQVc7Ozs7Ozs7OENBQzNGLDhEQUFDZ0g7O3NEQUFJLDhEQUFDdUI7c0RBQU87Ozs7Ozt3Q0FBMkI7d0NBQUU3SSxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWE4QyxJQUFJLENBQUNiLE1BQU0sS0FBSTs7Ozs7Ozs4Q0FDdEUsOERBQUNxRjs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUEwQjt3Q0FBRWxKLFdBQVd5QyxJQUFJO3dDQUFDOzs7Ozs7OzhDQUN6RCw4REFBQ2tGOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQXVCO3dDQUFFOUgsTUFBTUMsSUFBSSxDQUFDckIsV0FBVzhHLE1BQU0sSUFBSXBELE1BQU0sQ0FBQ3lGLENBQUFBLElBQUtBLEVBQUU1SCxNQUFNLENBQUM2QyxRQUFRLENBQUMsV0FBVzlCLE1BQU07d0NBQUM7Ozs7Ozs7OENBQ3RILDhEQUFDcUY7O3NEQUFJLDhEQUFDdUI7c0RBQU87Ozs7Ozt3Q0FBNkI7d0NBQUU5SCxNQUFNQyxJQUFJLENBQUNyQixXQUFXOEcsTUFBTSxJQUFJcEQsTUFBTSxDQUFDeUYsQ0FBQUEsSUFBS0EsRUFBRTVILE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxhQUFhK0UsRUFBRXJHLFVBQVUsRUFBRVIsTUFBTTt3Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUXJKakMsNkJBQ0MsOERBQUNzSDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNNO29DQUFLTixXQUFVOzhDQUEyQjs7Ozs7Ozs7Ozs7MENBRzdDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ007b0NBQUtOLFdBQVU7OENBQVk7Ozs7Ozs7Ozs7OzBDQUc5Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNNO29DQUFLTixXQUFVOzhDQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSzdDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUF3RDs7Ozs7OzBDQUd2RSw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQWdFOzs7Ozs7MENBRy9FLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7Ozs7Ozs7OztrQ0FJaEQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNadkgsWUFBWThDLElBQUksQ0FBQ1AsR0FBRyxDQUFDLENBQUN3RyxLQUFLakI7Z0NBY05pQixXQUdBQSxZQUdEQSxZQUdBQSxZQUVvRUEsWUFDaEZBLFlBWVlBLFlBS1pBLFlBY0FBLFVBS1lBLFdBRW9FQSxXQUNoRkEsV0FVWUEsV0FHQUEsV0FHQ0EsV0FHQUE7NEJBbkZwQixNQUFNQyxRQUFRekUsS0FBS1ksR0FBRyxDQUFDNEQsSUFBSXBHLFdBQVcsR0FBR3JDLG1CQUFtQjs0QkFDNUQsTUFBTTJJLGFBQWFGLElBQUlwRyxXQUFXLEdBQUdyQzs0QkFDckMsTUFBTTRJLFlBQVlILElBQUlwRyxXQUFXLEdBQUdyQzs0QkFFcEMscUJBQ0UsOERBQUNnSDtnQ0FFQ0MsV0FBVywyQ0FFVixPQURDeUIsUUFBUSxpQkFBaUJsQixRQUFRLE1BQU0sSUFBSSxhQUFhOztrREFJMUQsOERBQUNSO3dDQUFJQyxXQUFXLHdDQUFvRyxPQUE1RDBCLGFBQWEsK0JBQStCO2tEQUNqRy9CLGNBQWE2QixZQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsZ0NBQUFBLFVBQVVwRCxZQUFZOzs7Ozs7a0RBRXRDLDhEQUFDMkI7d0NBQUlDLFdBQVcsd0NBQW9HLE9BQTVEMEIsYUFBYSwrQkFBK0I7a0RBQ2pHL0IsY0FBYTZCLGFBQUFBLElBQUk1RixJQUFJLGNBQVI0RixpQ0FBQUEsV0FBVXJELE1BQU07Ozs7OztrREFFaEMsOERBQUM0Qjt3Q0FBSUMsV0FBVTtrREFDWlIsYUFBWWdDLGFBQUFBLElBQUk1RixJQUFJLGNBQVI0RixpQ0FBQUEsV0FBVW5ELEdBQUc7Ozs7OztrREFFNUIsOERBQUMwQjt3Q0FBSUMsV0FBVTtrREFDWlIsYUFBWWdDLGFBQUFBLElBQUk1RixJQUFJLGNBQVI0RixpQ0FBQUEsV0FBVWhELEdBQUc7Ozs7OztrREFFNUIsOERBQUN1Qjt3Q0FBSUMsV0FBVyxvREFBcUYsT0FBakNGLGdCQUFlMEIsYUFBQUEsSUFBSTVGLElBQUksY0FBUjRGLGlDQUFBQSxXQUFVdkQsTUFBTTtrREFDaEd1RCxFQUFBQSxhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVV2RCxNQUFNLGtCQUNmOztnREFDR3VELElBQUk1RixJQUFJLENBQUNxQyxNQUFNLEdBQUcsSUFBSSxNQUFNO2dEQUFJdUQsSUFBSTVGLElBQUksQ0FBQ3FDLE1BQU0sQ0FBQ3lCLE9BQU8sQ0FBQzs4REFDekQsOERBQUNLO29EQUFJQyxXQUFVOzt3REFBVTt3REFDckJ3QixJQUFJNUYsSUFBSSxDQUFDc0MsYUFBYSxHQUFHLElBQUksTUFBTTt3REFBSXNELElBQUk1RixJQUFJLENBQUNzQyxhQUFhLENBQUN3QixPQUFPLENBQUM7d0RBQUc7Ozs7Ozs7OzJEQUc3RTs7Ozs7O2tEQUVOLDhEQUFDSzt3Q0FBSUMsV0FBVyxrREFFZixPQURDMEIsYUFBYSxtQkFBbUI7a0RBRS9CbEMsYUFBWWdDLGFBQUFBLElBQUk1RixJQUFJLGNBQVI0RixpQ0FBQUEsV0FBVXJJLEdBQUc7Ozs7OztrREFFNUIsOERBQUM0Rzt3Q0FBSUMsV0FBVywyRUFFZixPQURDMEIsYUFBYSxtQkFBbUI7a0RBRS9CRixFQUFBQSxhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVVsSSxVQUFVLEtBQUk7Ozs7OztrREFJM0IsOERBQUN5Rzt3Q0FBSUMsV0FBVyx5RUFFZixPQURDeUIsUUFBUSxrQ0FBa0M7a0RBRXpDRCxJQUFJcEcsV0FBVzs7Ozs7O2tEQUlsQiw4REFBQzJFO3dDQUFJQyxXQUFXLGtEQUVmLE9BREMyQixZQUFZLGlCQUFpQjtrREFFNUJILEVBQUFBLFdBQUFBLElBQUkzRixHQUFHLGNBQVAyRiwrQkFBQUEsU0FBU2xJLFVBQVUsS0FBSTs7Ozs7O2tEQUUxQiw4REFBQ3lHO3dDQUFJQyxXQUFXLGtEQUVmLE9BREMyQixZQUFZLGlCQUFpQjtrREFFNUJuQyxhQUFZZ0MsWUFBQUEsSUFBSTNGLEdBQUcsY0FBUDJGLGdDQUFBQSxVQUFTckksR0FBRzs7Ozs7O2tEQUUzQiw4REFBQzRHO3dDQUFJQyxXQUFXLG9EQUFvRixPQUFoQ0YsZ0JBQWUwQixZQUFBQSxJQUFJM0YsR0FBRyxjQUFQMkYsZ0NBQUFBLFVBQVN2RCxNQUFNO2tEQUMvRnVELEVBQUFBLFlBQUFBLElBQUkzRixHQUFHLGNBQVAyRixnQ0FBQUEsVUFBU3ZELE1BQU0sa0JBQ2Q7O2dEQUNHdUQsSUFBSTNGLEdBQUcsQ0FBQ29DLE1BQU0sR0FBRyxJQUFJLE1BQU07Z0RBQUl1RCxJQUFJM0YsR0FBRyxDQUFDb0MsTUFBTSxDQUFDeUIsT0FBTyxDQUFDOzhEQUN2RCw4REFBQ0s7b0RBQUlDLFdBQVU7O3dEQUFVO3dEQUNyQndCLElBQUkzRixHQUFHLENBQUNxQyxhQUFhLEdBQUcsSUFBSSxNQUFNO3dEQUFJc0QsSUFBSTNGLEdBQUcsQ0FBQ3FDLGFBQWEsQ0FBQ3dCLE9BQU8sQ0FBQzt3REFBRzs7Ozs7Ozs7MkRBRzNFOzs7Ozs7a0RBRU4sOERBQUNLO3dDQUFJQyxXQUFVO2tEQUNaUixhQUFZZ0MsWUFBQUEsSUFBSTNGLEdBQUcsY0FBUDJGLGdDQUFBQSxVQUFTaEQsR0FBRzs7Ozs7O2tEQUUzQiw4REFBQ3VCO3dDQUFJQyxXQUFVO2tEQUNaUixhQUFZZ0MsWUFBQUEsSUFBSTNGLEdBQUcsY0FBUDJGLGdDQUFBQSxVQUFTbkQsR0FBRzs7Ozs7O2tEQUUzQiw4REFBQzBCO3dDQUFJQyxXQUFXLHdDQUFpRyxPQUF6RDJCLFlBQVksNkJBQTZCO2tEQUM5RmhDLGNBQWE2QixZQUFBQSxJQUFJM0YsR0FBRyxjQUFQMkYsZ0NBQUFBLFVBQVNyRCxNQUFNOzs7Ozs7a0RBRS9CLDhEQUFDNEI7d0NBQUlDLFdBQVcsd0NBQWlHLE9BQXpEMkIsWUFBWSw2QkFBNkI7a0RBQzlGaEMsY0FBYTZCLFlBQUFBLElBQUkzRixHQUFHLGNBQVAyRixnQ0FBQUEsVUFBU3BELFlBQVk7Ozs7Ozs7K0JBN0VoQ29ELElBQUlwRyxXQUFXOzs7Ozt3QkFpRjFCOzs7Ozs7Ozs7Ozs7WUFNTDNDLDZCQUNDLDhEQUFDc0g7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTs4Q0FBSzs7Ozs7OzhDQUNOLDhEQUFDQTs4Q0FBSzs7Ozs7OzhDQUNOLDhEQUFDQTs7d0NBQUs7d0NBQVM3SCxZQUFZOEMsSUFBSSxDQUFDYixNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7O3NDQUV6Qyw4REFBQ3FGOzRCQUFJQyxXQUFVOztnQ0FBd0I7Z0NBQ3RCLElBQUk1RCxLQUFLM0QsWUFBWTBELFNBQVMsRUFBRXlGLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTy9FO0dBcmhCd0J6SjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9PcHRpb25DaGFpbi50c3g/NWM4MSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBPcHRpb25DaGFpbkRhdGEsIE9wdGlvbkNoYWluUm93LCBPcHRpb25EYXRhLCBFeHBpcnlEYXRhLCBNYXJrZXREYXRhIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5pbnRlcmZhY2UgT3B0aW9uQ2hhaW5Qcm9wcyB7XG4gIG1hcmtldERhdGE6IE1hcDxzdHJpbmcsIE1hcmtldERhdGE+O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPcHRpb25DaGFpbih7IG1hcmtldERhdGEgfTogT3B0aW9uQ2hhaW5Qcm9wcykge1xuICBjb25zdCBbZXhwaXJ5RGF0YSwgc2V0RXhwaXJ5RGF0YV0gPSB1c2VTdGF0ZTxFeHBpcnlEYXRhIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZEV4cGlyeSwgc2V0U2VsZWN0ZWRFeHBpcnldID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtvcHRpb25DaGFpbiwgc2V0T3B0aW9uQ2hhaW5dID0gdXNlU3RhdGU8T3B0aW9uQ2hhaW5EYXRhIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW25pZnR5U3BvdFByaWNlLCBzZXROaWZ0eVNwb3RQcmljZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuXG4gIC8vIOKchSBGSVhFRDogR2V0IE5JRlRZIHNwb3QgcHJpY2UgZnJvbSBzdWJzY3JpYmVkIG1hcmtldCBkYXRhIChzZWN1cml0eSBJRCAxMywgZXhjaGFuZ2UgSURYX0kpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g4pyFIEZJWEVEOiBEaXJlY3QgbG9va3VwIGZvciBOSUZUWSBzcG90IChzZWN1cml0eSBJRCAxMykgLSBzaG91bGQgYmUgc3Vic2NyaWJlZCB3aXRoIElEWF9JIGV4Y2hhbmdlXG4gICAgY29uc3QgbmlmdHlEYXRhID0gbWFya2V0RGF0YS5nZXQoJzEzJyk7XG4gICAgaWYgKG5pZnR5RGF0YSAmJiBuaWZ0eURhdGEubHRwID4gMCkge1xuICAgICAgc2V0TmlmdHlTcG90UHJpY2UobmlmdHlEYXRhLmx0cCk7XG4gICAgICBjb25zb2xlLmxvZygnW1NQT1RdIPCfjq8gTklGVFkgU3BvdCBQcmljZSBVcGRhdGVkOicsIG5pZnR5RGF0YS5sdHAsICdmcm9tIHNlY3VyaXR5IElEIDEzIChJRFhfSSknKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyDinIUgRU5IQU5DRUQ6IEZhbGxiYWNrIHNlYXJjaCBmb3IgYW55IE5JRlRZIGluZGV4IGRhdGEgd2l0aCBiZXR0ZXIgbG9nZ2luZ1xuICAgIGZvciAoY29uc3QgW3NlY3VyaXR5SWQsIGRhdGFdIG9mIEFycmF5LmZyb20obWFya2V0RGF0YS5lbnRyaWVzKCkpKSB7XG4gICAgICBpZiAoZGF0YS5zeW1ib2wgPT09ICdOSUZUWScgJiYgZGF0YS5sdHAgPiAwKSB7XG4gICAgICAgIHNldE5pZnR5U3BvdFByaWNlKGRhdGEubHRwKTtcbiAgICAgICAgY29uc29sZS5sb2coJ1tTUE9UXSDwn46vIE5JRlRZIFNwb3QgUHJpY2UgRm91bmQ6JywgZGF0YS5sdHAsICdmcm9tIHNlY3VyaXR5OicsIHNlY3VyaXR5SWQsICdleGNoYW5nZTonLCBkYXRhLmV4Y2hhbmdlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOKchSBGSVhFRDogT25seSB1c2UgbW9jayBwcmljZSBpZiBhYnNvbHV0ZWx5IG5vIHJlYWwgZGF0YSBpcyBhdmFpbGFibGUgYW5kIGxvZyB3YXJuaW5nXG4gICAgaWYgKG5pZnR5U3BvdFByaWNlID09PSAwKSB7XG4gICAgICBjb25zdCBtb2NrUHJpY2UgPSAyNDg1MDtcbiAgICAgIHNldE5pZnR5U3BvdFByaWNlKG1vY2tQcmljZSk7XG4gICAgICBjb25zb2xlLndhcm4oJ1tTUE9UXSDimqDvuI8gVXNpbmcgbW9jayBOSUZUWSBTcG90IFByaWNlOicsIG1vY2tQcmljZSwgJyhJTkRFWCBkYXRhIG5vdCBhdmFpbGFibGUgLSBjaGVjayBzdWJzY3JpcHRpb24pJyk7XG4gICAgfVxuICB9LCBbbWFya2V0RGF0YV0pO1xuXG4gIC8vIEZldGNoIGV4cGlyeSBkYXRlcyBvbiBjb21wb25lbnQgbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEV4cGlyeURhdGVzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBCdWlsZCBvcHRpb24gY2hhaW4gd2hlbiBleHBpcnkgaXMgc2VsZWN0ZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRFeHBpcnkgJiYgbmlmdHlTcG90UHJpY2UgPiAwKSB7XG4gICAgICBidWlsZE9wdGlvbkNoYWluKCk7XG4gICAgfVxuICB9LCBbc2VsZWN0ZWRFeHBpcnksIG5pZnR5U3BvdFByaWNlLCBtYXJrZXREYXRhXSk7XG5cbiAgY29uc3QgZmV0Y2hFeHBpcnlEYXRlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL25pZnR5LWV4cGlyeScpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBleHBpcnkgZGF0ZXM6ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIHNldEV4cGlyeURhdGEocmVzdWx0LmRhdGEpO1xuICAgICAgICAvLyBBdXRvLXNlbGVjdCBmaXJzdCBleHBpcnlcbiAgICAgICAgaWYgKHJlc3VsdC5kYXRhLmV4cGlyaWVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBzZXRTZWxlY3RlZEV4cGlyeShyZXN1bHQuZGF0YS5leHBpcmllc1swXSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGZldGNoIGV4cGlyeSBkYXRlcycpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZXhwaXJ5IGRhdGVzOicsIGVycik7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGJ1aWxkT3B0aW9uQ2hhaW4gPSAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEV4cGlyeSB8fCBuaWZ0eVNwb3RQcmljZSA8PSAwKSByZXR1cm47XG5cbiAgICBjb25zb2xlLmxvZygn8J+UlyBCdWlsZGluZyBvcHRpb24gY2hhaW4gZm9yIGV4cGlyeTonLCBzZWxlY3RlZEV4cGlyeSwgJ3Nwb3Q6JywgbmlmdHlTcG90UHJpY2UpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIFRvdGFsIG1hcmtldCBkYXRhIGVudHJpZXM6JywgbWFya2V0RGF0YS5zaXplKTtcblxuICAgIC8vIERlYnVnOiBTaG93IHNhbXBsZSBtYXJrZXQgZGF0YSBlbnRyaWVzXG4gICAgY29uc3Qgc2FtcGxlRW50cmllcyA9IEFycmF5LmZyb20obWFya2V0RGF0YS5lbnRyaWVzKCkpLnNsaWNlKDAsIDEwKTtcbiAgICBjb25zb2xlLmxvZygn8J+TiyBTYW1wbGUgbWFya2V0IGRhdGEgZW50cmllczonLCBzYW1wbGVFbnRyaWVzLm1hcCgoW2lkLCBkYXRhXSkgPT4gKHtcbiAgICAgIHNlY3VyaXR5SWQ6IGlkLFxuICAgICAgc3ltYm9sOiBkYXRhLnN5bWJvbCxcbiAgICAgIGV4cGlyeURhdGU6IGRhdGEuZXhwaXJ5RGF0ZSxcbiAgICAgIG9wdGlvblR5cGU6IGRhdGEub3B0aW9uVHlwZSxcbiAgICAgIHN0cmlrZVByaWNlOiBkYXRhLnN0cmlrZVByaWNlXG4gICAgfSkpKTtcblxuICAgIC8vIEdlbmVyYXRlIHN0cmlrZSBwcmljZXMgYXJvdW5kIHNwb3QgcHJpY2VcbiAgICBjb25zdCBzdHJpa2VzID0gZ2VuZXJhdGVTdHJpa2VzKG5pZnR5U3BvdFByaWNlKTtcbiAgICBjb25zb2xlLmxvZygn8J+OryBHZW5lcmF0ZWQgc3RyaWtlczonLCBzdHJpa2VzKTtcblxuICAgIC8vIEJ1aWxkIG9wdGlvbiBjaGFpbiByb3dzXG4gICAgY29uc3Qgcm93czogT3B0aW9uQ2hhaW5Sb3dbXSA9IHN0cmlrZXMubWFwKHN0cmlrZSA9PiB7XG4gICAgICBjb25zdCBjYWxsRGF0YSA9IGZpbmRPcHRpb25EYXRhKHN0cmlrZSwgJ0NFJyk7XG4gICAgICBjb25zdCBwdXREYXRhID0gZmluZE9wdGlvbkRhdGEoc3RyaWtlLCAnUEUnKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RyaWtlUHJpY2U6IHN0cmlrZSxcbiAgICAgICAgY2FsbDogY2FsbERhdGEsXG4gICAgICAgIHB1dDogcHV0RGF0YVxuICAgICAgfTtcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5OIIE9wdGlvbiBjaGFpbiByb3dzIGJ1aWx0OicsIHJvd3MubGVuZ3RoLCAncm93cycpO1xuICAgIGNvbnNvbGUubG9nKCfinIUgRm91bmQgb3B0aW9uczonLCByb3dzLmZpbHRlcihyID0+IHIuY2FsbCB8fCByLnB1dCkubGVuZ3RoLCAnc3RyaWtlcyB3aXRoIGRhdGEnKTtcblxuICAgIHNldE9wdGlvbkNoYWluKHtcbiAgICAgIHVuZGVybHlpbmc6ICdOSUZUWScsXG4gICAgICBzcG90UHJpY2U6IG5pZnR5U3BvdFByaWNlLFxuICAgICAgZXhwaXJ5OiBzZWxlY3RlZEV4cGlyeSxcbiAgICAgIHJvd3MsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBnZW5lcmF0ZVN0cmlrZXMgPSAoc3BvdFByaWNlOiBudW1iZXIpOiBudW1iZXJbXSA9PiB7XG4gICAgLy8gRmlyc3QsIGdldCBhbGwgYXZhaWxhYmxlIHN0cmlrZXMgZnJvbSBtYXJrZXQgZGF0YSBmb3IgdGhlIHNlbGVjdGVkIGV4cGlyeVxuICAgIGNvbnN0IGF2YWlsYWJsZVN0cmlrZXMgPSBuZXcgU2V0PG51bWJlcj4oKTtcblxuICAgIGZvciAoY29uc3QgW3NlY3VyaXR5SWQsIGRhdGFdIG9mIEFycmF5LmZyb20obWFya2V0RGF0YS5lbnRyaWVzKCkpKSB7XG4gICAgICBpZiAoZGF0YS5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZJykgJiZcbiAgICAgICAgICBkYXRhLnN5bWJvbC5pbmNsdWRlcyhmb3JtYXRFeHBpcnlGb3JTeW1ib2woc2VsZWN0ZWRFeHBpcnkpKSkge1xuXG4gICAgICAgIC8vIEV4dHJhY3Qgc3RyaWtlIGZyb20gc3ltYm9sIChlLmcuLCBOSUZUWS1KdW4yMDI1LTI0ODUwLUNFKVxuICAgICAgICBjb25zdCBzeW1ib2xQYXJ0cyA9IGRhdGEuc3ltYm9sLnNwbGl0KCctJyk7XG4gICAgICAgIGlmIChzeW1ib2xQYXJ0cy5sZW5ndGggPj0gNCkge1xuICAgICAgICAgIGNvbnN0IHN0cmlrZSA9IHBhcnNlRmxvYXQoc3ltYm9sUGFydHNbMl0pO1xuICAgICAgICAgIGlmICghaXNOYU4oc3RyaWtlKSkge1xuICAgICAgICAgICAgYXZhaWxhYmxlU3RyaWtlcy5hZGQoc3RyaWtlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoYXZhaWxhYmxlU3RyaWtlcy5zaXplID09PSAwKSB7XG4gICAgICAvLyBGYWxsYmFjazogZ2VuZXJhdGUgc3RyaWtlcyBhcm91bmQgc3BvdCBwcmljZVxuICAgICAgY29uc3Qgc3RyaWtlczogbnVtYmVyW10gPSBbXTtcbiAgICAgIGNvbnN0IGJhc2VTdHJpa2UgPSBNYXRoLnJvdW5kKHNwb3RQcmljZSAvIDUwKSAqIDUwO1xuICAgICAgZm9yIChsZXQgaSA9IC0xMDsgaSA8PSAxMDsgaSsrKSB7XG4gICAgICAgIHN0cmlrZXMucHVzaChiYXNlU3RyaWtlICsgKGkgKiA1MCkpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHN0cmlrZXMuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuICAgIH1cblxuICAgIC8vIENvbnZlcnQgdG8gc29ydGVkIGFycmF5XG4gICAgY29uc3QgYWxsU3RyaWtlcyA9IEFycmF5LmZyb20oYXZhaWxhYmxlU3RyaWtlcykuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuXG4gICAgLy8gRmluZCBBVE0gc3RyaWtlIChjbG9zZXN0IHRvIHNwb3QgcHJpY2UpXG4gICAgY29uc3QgYXRtU3RyaWtlID0gYWxsU3RyaWtlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+XG4gICAgICBNYXRoLmFicyhjdXJyIC0gc3BvdFByaWNlKSA8IE1hdGguYWJzKHByZXYgLSBzcG90UHJpY2UpID8gY3VyciA6IHByZXZcbiAgICApO1xuXG4gICAgY29uc29sZS5sb2coYFtTVFJJS0VdIPCfjq8gQVRNIFN0cmlrZSBpZGVudGlmaWVkOiAke2F0bVN0cmlrZX0gKFNwb3Q6ICR7c3BvdFByaWNlfSlgKTtcbiAgICBjb25zb2xlLmxvZyhgW1NUUklLRV0g8J+TiiBBdmFpbGFibGUgc3RyaWtlczogJHthbGxTdHJpa2VzLmxlbmd0aH1gKTtcblxuICAgIC8vIOKchSBFTkhBTkNFRDogU2VsZWN0IG1vcmUgc3RyaWtlcyBhcm91bmQgQVRNICjCsTEyIHN0cmlrZXMgZm9yIGJldHRlciBjb3ZlcmFnZSlcbiAgICBjb25zdCBhdG1JbmRleCA9IGFsbFN0cmlrZXMuaW5kZXhPZihhdG1TdHJpa2UpO1xuICAgIGNvbnN0IHNlbGVjdGVkU3RyaWtlcyA9IFtcbiAgICAgIC4uLmFsbFN0cmlrZXMuc2xpY2UoTWF0aC5tYXgoMCwgYXRtSW5kZXggLSAxMiksIGF0bUluZGV4KSxcbiAgICAgIGF0bVN0cmlrZSxcbiAgICAgIC4uLmFsbFN0cmlrZXMuc2xpY2UoYXRtSW5kZXggKyAxLCBhdG1JbmRleCArIDEzKVxuICAgIF07XG5cbiAgICBjb25zb2xlLmxvZyhgW1NUUklLRV0g4pyFIFNlbGVjdGVkICR7c2VsZWN0ZWRTdHJpa2VzLmxlbmd0aH0gc3RyaWtlcyBhcm91bmQgQVRNOmAsIHNlbGVjdGVkU3RyaWtlcyk7XG5cbiAgICByZXR1cm4gc2VsZWN0ZWRTdHJpa2VzO1xuICB9O1xuXG4gIGNvbnN0IGZpbmRPcHRpb25EYXRhID0gKHN0cmlrZTogbnVtYmVyLCBvcHRpb25UeXBlOiAnQ0UnIHwgJ1BFJyk6IE9wdGlvbkRhdGEgfCBudWxsID0+IHtcbiAgICAvLyDinIUgRklYRUQ6IE1hdGNoIGJ5IGV4YWN0IGV4cGlyeSBkYXRlIGZyb20gQ1NWIFNNX0VYUElSWV9EQVRFIGNvbHVtblxuICAgIGZvciAoY29uc3QgW3NlY3VyaXR5SWQsIGRhdGFdIG9mIEFycmF5LmZyb20obWFya2V0RGF0YS5lbnRyaWVzKCkpKSB7XG4gICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgTklGVFkgb3B0aW9uIHdpdGggbWF0Y2hpbmcgY3JpdGVyaWFcbiAgICAgIGlmIChkYXRhLnN5bWJvbC5pbmNsdWRlcygnTklGVFktJykgJiYgIC8vIEV4YWN0IE5JRlRZIG9wdGlvbnMgKG5vdCBCQU5LTklGVFksIGV0Yy4pXG4gICAgICAgICAgZGF0YS5zeW1ib2wuaW5jbHVkZXMoYC0ke3N0cmlrZX0tJHtvcHRpb25UeXBlfWApICYmXG4gICAgICAgICAgZGF0YS5leHBpcnlEYXRlID09PSBzZWxlY3RlZEV4cGlyeSkgeyAvLyDinIUgRGlyZWN0IGV4cGlyeSBkYXRlIG1hdGNoIGZyb20gQ1NWIFNNX0VYUElSWV9EQVRFXG5cbiAgICAgICAgY29uc29sZS5sb2coYFtPUFRJT05dIOKchSBGb3VuZCAke29wdGlvblR5cGV9ICR7c3RyaWtlfTogJHtkYXRhLnN5bWJvbH0gKEV4cGlyeTogJHtkYXRhLmV4cGlyeURhdGV9KWApO1xuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc2VjdXJpdHlJZCxcbiAgICAgICAgICBzeW1ib2w6IGRhdGEuc3ltYm9sLFxuICAgICAgICAgIGV4Y2hhbmdlOiBkYXRhLmV4Y2hhbmdlLFxuICAgICAgICAgIHN0cmlrZVByaWNlOiBzdHJpa2UsXG4gICAgICAgICAgb3B0aW9uVHlwZSxcbiAgICAgICAgICBleHBpcnlEYXRlOiBzZWxlY3RlZEV4cGlyeSxcbiAgICAgICAgICBsdHA6IGRhdGEubHRwIHx8IDAsXG4gICAgICAgICAgY2hhbmdlOiBkYXRhLmNoYW5nZSB8fCAwLFxuICAgICAgICAgIGNoYW5nZVBlcmNlbnQ6IGRhdGEuY2hhbmdlUGVyY2VudCB8fCAwLFxuICAgICAgICAgIHZvbHVtZTogZGF0YS52b2x1bWUgfHwgMCxcbiAgICAgICAgICBvcGVuSW50ZXJlc3Q6IGRhdGEub3BlbkludGVyZXN0LFxuICAgICAgICAgIGJpZDogZGF0YS5tYXJrZXREZXB0aD8uWzBdPy5iaWRQcmljZSB8fCBkYXRhLmJpZCxcbiAgICAgICAgICBhc2s6IGRhdGEubWFya2V0RGVwdGg/LlswXT8uYXNrUHJpY2UgfHwgZGF0YS5hc2ssXG4gICAgICAgICAgYmlkUXR5OiBkYXRhLm1hcmtldERlcHRoPy5bMF0/LmJpZFF0eSB8fCBkYXRhLmJpZFF0eSxcbiAgICAgICAgICBhc2tRdHk6IGRhdGEubWFya2V0RGVwdGg/LlswXT8uYXNrUXR5IHx8IGRhdGEuYXNrUXR5LFxuICAgICAgICAgIGhpZ2g6IGRhdGEuaGlnaCxcbiAgICAgICAgICBsb3c6IGRhdGEubG93LFxuICAgICAgICAgIG9wZW46IGRhdGEub3BlbixcbiAgICAgICAgICBjbG9zZTogZGF0YS5jbG9zZSxcbiAgICAgICAgICB0aW1lc3RhbXA6IGRhdGEudGltZXN0YW1wXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRGVidWc6IExvZyB3aGVuIG9wdGlvbiBub3QgZm91bmRcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgIGNvbnNvbGUubG9nKGBbT1BUSU9OXSDinYwgTm90IGZvdW5kICR7b3B0aW9uVHlwZX0gJHtzdHJpa2V9IGZvciBleHBpcnkgJHtzZWxlY3RlZEV4cGlyeX1gKTtcblxuICAgICAgLy8gU2hvdyBhdmFpbGFibGUgb3B0aW9ucyBmb3IgZGVidWdnaW5nXG4gICAgICBjb25zdCBhdmFpbGFibGVPcHRpb25zID0gQXJyYXkuZnJvbShtYXJrZXREYXRhLnZhbHVlcygpKVxuICAgICAgICAuZmlsdGVyKGRhdGEgPT4gZGF0YS5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZLScpICYmIGRhdGEuc3ltYm9sLmluY2x1ZGVzKGAtJHtvcHRpb25UeXBlfWApKVxuICAgICAgICAuc2xpY2UoMCwgNSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGBbREVCVUddIEF2YWlsYWJsZSAke29wdGlvblR5cGV9IG9wdGlvbnM6YCwgYXZhaWxhYmxlT3B0aW9ucy5tYXAob3B0ID0+XG4gICAgICAgIGAke29wdC5zeW1ib2x9IChFeHBpcnk6ICR7b3B0LmV4cGlyeURhdGV9KWBcbiAgICAgICkpO1xuICAgIH1cblxuICAgIHJldHVybiBudWxsO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdEV4cGlyeUZvclN5bWJvbCA9IChleHBpcnk6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gICAgLy8gQ29udmVydCBZWVlZLU1NLUREIHRvIGZvcm1hdCB1c2VkIGluIHN5bWJvbHMgKGUuZy4sIEp1bjIwMjUpXG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGV4cGlyeSk7XG4gICAgY29uc3QgbW9udGhzID0gWydKYW4nLCAnRmViJywgJ01hcicsICdBcHInLCAnTWF5JywgJ0p1bicsXG4gICAgICAgICAgICAgICAgICAgJ0p1bCcsICdBdWcnLCAnU2VwJywgJ09jdCcsICdOb3YnLCAnRGVjJ107XG4gICAgcmV0dXJuIGAke21vbnRoc1tkYXRlLmdldE1vbnRoKCldfSR7ZGF0ZS5nZXRGdWxsWWVhcigpfWA7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0UHJpY2UgPSAocHJpY2U6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFwcmljZSB8fCBwcmljZSA8PSAwKSByZXR1cm4gJ04vQSc7XG4gICAgcmV0dXJuIGDigrkke3ByaWNlLnRvRml4ZWQoMil9YDtcbiAgfTtcblxuICBjb25zdCBmb3JtYXROdW1iZXIgPSAobnVtOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmICghbnVtKSByZXR1cm4gJ04vQSc7XG4gICAgcmV0dXJuIG51bS50b0xvY2FsZVN0cmluZygpO1xuICB9O1xuXG4gIGNvbnN0IGdldENoYW5nZUNvbG9yID0gKGNoYW5nZTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIWNoYW5nZSkgcmV0dXJuICd0ZXh0LWdyYXktNDAwJztcbiAgICByZXR1cm4gY2hhbmdlID4gMCA/ICd0ZXh0LWdyZWVuLTQwMCcgOiBjaGFuZ2UgPCAwID8gJ3RleHQtcmVkLTQwMCcgOiAndGV4dC1ncmF5LTQwMCc7XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIG9wdGlvbiBjaGFpbi4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwIGZvbnQtbWVkaXVtXCI+RXJyb3IgTG9hZGluZyBPcHRpb24gQ2hhaW48L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgdGV4dC1zbSBtdC0xXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICBvbkNsaWNrPXtmZXRjaEV4cGlyeURhdGVzfVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgcHgtMyBweS0xIGJnLXJlZC02MDAgdGV4dC13aGl0ZSB0ZXh0LXNtIHJvdW5kZWQgaG92ZXI6YmctcmVkLTcwMFwiXG4gICAgICAgID5cbiAgICAgICAgICBSZXRyeVxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgbWluLWgtc2NyZWVuXCI+XG4gICAgICB7LyogSGVhZGVyIFNlY3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBweC02IHB5LTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+T3B0aW9uczwvaDE+XG5cbiAgICAgICAgICAgIHsvKiBUYWIgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgIPCflI0gTklGVFlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICBTdHJhdGVneSBidWlsZGVyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgICAgQ2xhc3NcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICBWb2xhdGlsaXR5XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmlnaHQgc2lkZSBjb250cm9scyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgQnkgZXhwaXJhdGlvbiB8IGJ5IHN0cmlrZVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5OSUZUWSBTcG90Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShuaWZ0eVNwb3RQcmljZSl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAge25pZnR5U3BvdFByaWNlID09PSAyNDg1MCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+KE1vY2spPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFeHBpcnkgU2VsZWN0aW9uIC0gRW5oYW5jZWQgRm9ybWF0ICovfVxuICAgICAge2V4cGlyeURhdGEgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBweC02IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBvdmVyZmxvdy14LWF1dG8gcGItMlwiPlxuICAgICAgICAgICAge2V4cGlyeURhdGEuZXhwaXJpZXMuc2xpY2UoMCwgMTUpLm1hcCgoZXhwaXJ5LCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZXhwaXJ5KTtcbiAgICAgICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IGV4cGlyeSA9PT0gc2VsZWN0ZWRFeHBpcnk7XG4gICAgICAgICAgICAgIGNvbnN0IGlzVG9kYXkgPSBkYXRlLnRvRGF0ZVN0cmluZygpID09PSBuZXcgRGF0ZSgpLnRvRGF0ZVN0cmluZygpO1xuICAgICAgICAgICAgICBjb25zdCBpc0N1cnJlbnRXZWVrID0gTWF0aC5hYnMoZGF0ZS5nZXRUaW1lKCkgLSBuZXcgRGF0ZSgpLmdldFRpbWUoKSkgPCA3ICogMjQgKiA2MCAqIDYwICogMTAwMDtcblxuICAgICAgICAgICAgICAvLyBFbmhhbmNlZCBkYXRlIGZvcm1hdHRpbmdcbiAgICAgICAgICAgICAgY29uc3QgY3VycmVudFllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7XG4gICAgICAgICAgICAgIGNvbnN0IGV4cGlyeVllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7XG4gICAgICAgICAgICAgIGNvbnN0IG1vbnRoU2hvcnQgPSBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IG1vbnRoOiAnc2hvcnQnIH0pO1xuICAgICAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKTtcblxuICAgICAgICAgICAgICAvLyBTaG93IHllYXIgaWYgZGlmZmVyZW50IGZyb20gY3VycmVudCB5ZWFyXG4gICAgICAgICAgICAgIGNvbnN0IHNob3dZZWFyID0gZXhwaXJ5WWVhciAhPT0gY3VycmVudFllYXI7XG5cbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2V4cGlyeX1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkRXhwaXJ5KGV4cGlyeSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LXNocmluay0wIHB4LTMgcHktMiB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGJvcmRlciAke1xuICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmxhY2sgdGV4dC13aGl0ZSBib3JkZXItYmxhY2snXG4gICAgICAgICAgICAgICAgICAgICAgOiBpc1RvZGF5XG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctb3JhbmdlLTUwMCB0ZXh0LXdoaXRlIGJvcmRlci1vcmFuZ2UtNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogaXNDdXJyZW50V2Vla1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAgYm9yZGVyLWdyYXktMzAwIGhvdmVyOmJnLWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlIHRleHQtZ3JheS03MDAgYm9yZGVyLWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNTAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1pbi13LVs0MHB4XVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1ub3JtYWwgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHttb250aFNob3J0fXtzaG93WWVhciAmJiBgICR7ZXhwaXJ5WWVhci50b1N0cmluZygpLnNsaWNlKC0yKX1gfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LWJvbGQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtkYXl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBFbmhhbmNlZCBEZWJ1ZyBJbmZvICovfVxuICAgICAgICAgIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTMgdGV4dC14cyB0ZXh0LWdyYXktNTAwIGJnLWdyYXktMTAwIHAtMyByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5TZWxlY3RlZCBFeHBpcnk6PC9zdHJvbmc+IHtzZWxlY3RlZEV4cGlyeX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+TklGVFkgU3BvdDo8L3N0cm9uZz4g4oK5e25pZnR5U3BvdFByaWNlfSB7bmlmdHlTcG90UHJpY2UgPT09IDI0ODUwID8gJyhNb2NrKScgOiAnKExpdmUpJ308L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+QXZhaWxhYmxlIFN0cmlrZXM6PC9zdHJvbmc+IHtvcHRpb25DaGFpbj8ucm93cy5sZW5ndGggfHwgMH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+TWFya2V0IERhdGEgU2l6ZTo8L3N0cm9uZz4ge21hcmtldERhdGEuc2l6ZX0gaW5zdHJ1bWVudHM8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+TklGVFkgT3B0aW9uczo8L3N0cm9uZz4ge0FycmF5LmZyb20obWFya2V0RGF0YS52YWx1ZXMoKSkuZmlsdGVyKGQgPT4gZC5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZLScpKS5sZW5ndGh9IGZvdW5kPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPk9wdGlvbnMgd2l0aCBFeHBpcnk6PC9zdHJvbmc+IHtBcnJheS5mcm9tKG1hcmtldERhdGEudmFsdWVzKCkpLmZpbHRlcihkID0+IGQuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWS0nKSAmJiBkLmV4cGlyeURhdGUpLmxlbmd0aH0gbWF0Y2hlZDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIE9wdGlvbiBDaGFpbiBUYWJsZSAqL31cbiAgICAgIHtvcHRpb25DaGFpbiAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGVcIj5cbiAgICAgICAgICB7LyogVGFibGUgSGVhZGVycyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggYmctZ3JheS01MCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgey8qIENhbGxzIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTMgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGZvbnQtYm9sZFwiPkNhbGxzPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7LyogU3RyaWtlIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCB0ZXh0LWNlbnRlciBweS0zIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LWJvbGRcIj5TdHJpa2U8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBQdXRzIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTNcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGZvbnQtYm9sZFwiPlB1dHM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDb2x1bW4gSGVhZGVycyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggYmctZ3JheS0xMDAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgIHsvKiBDYWxsIGNvbHVtbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5PSTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+Vm9sdW1lPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5CaWQ8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkFzazwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+Q2hhbmdlPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5MVFA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMSBib3JkZXItciBib3JkZXItZ3JheS0yMDBcIj5TZWN1cml0eSBJRDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogU3RyaWtlICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIHRleHQtY2VudGVyIHB5LTIgcHgtMSBib3JkZXItciBib3JkZXItZ3JheS0yMDAgZm9udC1ib2xkXCI+U3RyaWtlPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBQdXQgY29sdW1ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPlNlY3VyaXR5IElEPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5MVFA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkNoYW5nZTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+QXNrPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5CaWQ8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPlZvbHVtZTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+T0k8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBUYWJsZSBCb2R5ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMTAwXCI+XG4gICAgICAgICAgICB7b3B0aW9uQ2hhaW4ucm93cy5tYXAoKHJvdywgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgaXNBVE0gPSBNYXRoLmFicyhyb3cuc3RyaWtlUHJpY2UgLSBuaWZ0eVNwb3RQcmljZSkgPD0gNTA7XG4gICAgICAgICAgICAgIGNvbnN0IGlzSVRNX0NhbGwgPSByb3cuc3RyaWtlUHJpY2UgPCBuaWZ0eVNwb3RQcmljZTtcbiAgICAgICAgICAgICAgY29uc3QgaXNJVE1fUHV0ID0gcm93LnN0cmlrZVByaWNlID4gbmlmdHlTcG90UHJpY2U7XG5cbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e3Jvdy5zdHJpa2VQcmljZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBpc0FUTSA/ICdiZy15ZWxsb3ctNTAnIDogaW5kZXggJSAyID09PSAwID8gJ2JnLXdoaXRlJyA6ICdiZy1ncmF5LTI1J1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgey8qIENhbGwgT3B0aW9ucyBEYXRhICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gJHtpc0lUTV9DYWxsID8gJ3RleHQtZ3JlZW4tNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cuY2FsbD8ub3BlbkludGVyZXN0KX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gJHtpc0lUTV9DYWxsID8gJ3RleHQtZ3JlZW4tNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cuY2FsbD8udm9sdW1lKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHJvdy5jYWxsPy5iaWQpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHJvdy5jYWxsPy5hc2spfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldENoYW5nZUNvbG9yKHJvdy5jYWxsPy5jaGFuZ2UpfWB9PlxuICAgICAgICAgICAgICAgICAgICB7cm93LmNhbGw/LmNoYW5nZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Jvdy5jYWxsLmNoYW5nZSA+IDAgPyAnKycgOiAnJ317cm93LmNhbGwuY2hhbmdlLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKHtyb3cuY2FsbC5jaGFuZ2VQZXJjZW50ID4gMCA/ICcrJyA6ICcnfXtyb3cuY2FsbC5jaGFuZ2VQZXJjZW50LnRvRml4ZWQoMSl9JSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogJ04vQSd9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIGZvbnQtYm9sZCAke1xuICAgICAgICAgICAgICAgICAgICBpc0lUTV9DYWxsID8gJ3RleHQtZ3JlZW4tNjAwJyA6ICd0ZXh0LWdyYXktNzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LmNhbGw/Lmx0cCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXhzIGZvbnQtbW9ubyBib3JkZXItciBib3JkZXItZ3JheS0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNJVE1fQ2FsbCA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1ncmF5LTUwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Jvdy5jYWxsPy5zZWN1cml0eUlkIHx8ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTdHJpa2UgUHJpY2UgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMjAgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gZm9udC1ib2xkIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCAke1xuICAgICAgICAgICAgICAgICAgICBpc0FUTSA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOiAndGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Jvdy5zdHJpa2VQcmljZX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogUHV0IE9wdGlvbnMgRGF0YSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXhzIGZvbnQtbW9ubyAke1xuICAgICAgICAgICAgICAgICAgICBpc0lUTV9QdXQgPyAndGV4dC1yZWQtNjAwJyA6ICd0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7cm93LnB1dD8uc2VjdXJpdHlJZCB8fCAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gZm9udC1ib2xkICR7XG4gICAgICAgICAgICAgICAgICAgIGlzSVRNX1B1dCA/ICd0ZXh0LXJlZC02MDAnIDogJ3RleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cucHV0Py5sdHApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldENoYW5nZUNvbG9yKHJvdy5wdXQ/LmNoYW5nZSl9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtyb3cucHV0Py5jaGFuZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyb3cucHV0LmNoYW5nZSA+IDAgPyAnKycgOiAnJ317cm93LnB1dC5jaGFuZ2UudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAoe3Jvdy5wdXQuY2hhbmdlUGVyY2VudCA+IDAgPyAnKycgOiAnJ317cm93LnB1dC5jaGFuZ2VQZXJjZW50LnRvRml4ZWQoMSl9JSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogJ04vQSd9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LnB1dD8uYXNrKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHJvdy5wdXQ/LmJpZCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtICR7aXNJVE1fUHV0ID8gJ3RleHQtcmVkLTcwMCBmb250LW1lZGl1bScgOiAndGV4dC1ncmF5LTcwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LnB1dD8udm9sdW1lKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gJHtpc0lUTV9QdXQgPyAndGV4dC1yZWQtNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cucHV0Py5vcGVuSW50ZXJlc3QpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAge29wdGlvbkNoYWluICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBweC02IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuPlNob3cgYWxsPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPlNob3dpbmcge29wdGlvbkNoYWluLnJvd3MubGVuZ3RofSBzdHJpa2VzIGFyb3VuZCBBVE08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIExhc3QgdXBkYXRlZDoge25ldyBEYXRlKG9wdGlvbkNoYWluLnRpbWVzdGFtcCkudG9Mb2NhbGVUaW1lU3RyaW5nKCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiT3B0aW9uQ2hhaW4iLCJtYXJrZXREYXRhIiwiZXhwaXJ5RGF0YSIsInNldEV4cGlyeURhdGEiLCJzZWxlY3RlZEV4cGlyeSIsInNldFNlbGVjdGVkRXhwaXJ5Iiwib3B0aW9uQ2hhaW4iLCJzZXRPcHRpb25DaGFpbiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsIm5pZnR5U3BvdFByaWNlIiwic2V0TmlmdHlTcG90UHJpY2UiLCJuaWZ0eURhdGEiLCJnZXQiLCJsdHAiLCJjb25zb2xlIiwibG9nIiwic2VjdXJpdHlJZCIsImRhdGEiLCJBcnJheSIsImZyb20iLCJlbnRyaWVzIiwic3ltYm9sIiwiZXhjaGFuZ2UiLCJtb2NrUHJpY2UiLCJ3YXJuIiwiZmV0Y2hFeHBpcnlEYXRlcyIsImJ1aWxkT3B0aW9uQ2hhaW4iLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJFcnJvciIsInN0YXR1c1RleHQiLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImV4cGlyaWVzIiwibGVuZ3RoIiwibWVzc2FnZSIsImVyciIsInNpemUiLCJzYW1wbGVFbnRyaWVzIiwic2xpY2UiLCJtYXAiLCJpZCIsImV4cGlyeURhdGUiLCJvcHRpb25UeXBlIiwic3RyaWtlUHJpY2UiLCJzdHJpa2VzIiwiZ2VuZXJhdGVTdHJpa2VzIiwicm93cyIsInN0cmlrZSIsImNhbGxEYXRhIiwiZmluZE9wdGlvbkRhdGEiLCJwdXREYXRhIiwiY2FsbCIsInB1dCIsImZpbHRlciIsInIiLCJ1bmRlcmx5aW5nIiwic3BvdFByaWNlIiwiZXhwaXJ5IiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsImF2YWlsYWJsZVN0cmlrZXMiLCJTZXQiLCJpbmNsdWRlcyIsImZvcm1hdEV4cGlyeUZvclN5bWJvbCIsInN5bWJvbFBhcnRzIiwic3BsaXQiLCJwYXJzZUZsb2F0IiwiaXNOYU4iLCJhZGQiLCJiYXNlU3RyaWtlIiwiTWF0aCIsInJvdW5kIiwiaSIsInB1c2giLCJzb3J0IiwiYSIsImIiLCJhbGxTdHJpa2VzIiwiYXRtU3RyaWtlIiwicmVkdWNlIiwicHJldiIsImN1cnIiLCJhYnMiLCJhdG1JbmRleCIsImluZGV4T2YiLCJzZWxlY3RlZFN0cmlrZXMiLCJtYXgiLCJjaGFuZ2UiLCJjaGFuZ2VQZXJjZW50Iiwidm9sdW1lIiwib3BlbkludGVyZXN0IiwiYmlkIiwibWFya2V0RGVwdGgiLCJiaWRQcmljZSIsImFzayIsImFza1ByaWNlIiwiYmlkUXR5IiwiYXNrUXR5IiwiaGlnaCIsImxvdyIsIm9wZW4iLCJjbG9zZSIsInByb2Nlc3MiLCJhdmFpbGFibGVPcHRpb25zIiwidmFsdWVzIiwib3B0IiwiZGF0ZSIsIm1vbnRocyIsImdldE1vbnRoIiwiZ2V0RnVsbFllYXIiLCJmb3JtYXRQcmljZSIsInByaWNlIiwidG9GaXhlZCIsImZvcm1hdE51bWJlciIsIm51bSIsInRvTG9jYWxlU3RyaW5nIiwiZ2V0Q2hhbmdlQ29sb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwiaDEiLCJzcGFuIiwiaW5kZXgiLCJpc1NlbGVjdGVkIiwiaXNUb2RheSIsInRvRGF0ZVN0cmluZyIsImlzQ3VycmVudFdlZWsiLCJnZXRUaW1lIiwiY3VycmVudFllYXIiLCJleHBpcnlZZWFyIiwibW9udGhTaG9ydCIsInRvTG9jYWxlRGF0ZVN0cmluZyIsIm1vbnRoIiwiZGF5IiwiZ2V0RGF0ZSIsInNob3dZZWFyIiwidG9TdHJpbmciLCJzdHJvbmciLCJkIiwicm93IiwiaXNBVE0iLCJpc0lUTV9DYWxsIiwiaXNJVE1fUHV0IiwidG9Mb2NhbGVUaW1lU3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});