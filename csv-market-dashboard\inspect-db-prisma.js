// Inspect database using Prisma
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function inspectDatabase() {
  try {
    console.log('🔍 Connecting to database using Prisma...');

    // Test connection by counting total instruments
    const totalCount = await prisma.instruments.count();
    console.log(`✅ Connected! Total instruments: ${totalCount}`);

    // Check for NIFTY options
    console.log('\n🎯 Checking for NIFTY options...');
    const niftyCount = await prisma.instruments.count({
      where: {
        SYMBOL_NAME: {
          startsWith: 'NIFTY-'
        },
        OPTION_TYPE: {
          in: ['CE', 'PE']
        }
      }
    });
    
    console.log(`NIFTY options found: ${niftyCount}`);

    if (niftyCount > 0) {
      // Get sample NIFTY options
      console.log('\n📊 Sample NIFTY options:');
      const sampleOptions = await prisma.instruments.findMany({
        where: {
          SYMBOL_NAME: {
            startsWith: 'NIFTY-'
          },
          OPTION_TYPE: {
            in: ['CE', 'PE']
          }
        },
        orderBy: [
          { SM_EXPIRY_DATE: 'asc' },
          { STRIKE_PRICE: 'asc' }
        ],
        take: 10
      });

      sampleOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.SYMBOL_NAME} (ID: ${option.SECURITY_ID})`);
        console.log(`   Expiry: ${option.SM_EXPIRY_DATE}, Strike: ${option.STRIKE_PRICE}, Type: ${option.OPTION_TYPE}`);
      });

      // Get available expiry dates for NIFTY options
      console.log('\n📅 Available NIFTY expiry dates:');
      const expiries = await prisma.instruments.findMany({
        where: {
          SYMBOL_NAME: {
            startsWith: 'NIFTY-'
          },
          OPTION_TYPE: {
            in: ['CE', 'PE']
          },
          SM_EXPIRY_DATE: {
            not: null
          }
        },
        select: {
          SM_EXPIRY_DATE: true
        },
        distinct: ['SM_EXPIRY_DATE'],
        orderBy: {
          SM_EXPIRY_DATE: 'asc'
        },
        take: 10
      });

      expiries.forEach((expiry, index) => {
        console.log(`${index + 1}. ${expiry.SM_EXPIRY_DATE}`);
      });

      // Get options around current NIFTY price (24850)
      console.log('\n🎯 NIFTY options around 24850 strike:');
      const nearbyOptions = await prisma.instruments.findMany({
        where: {
          SYMBOL_NAME: {
            startsWith: 'NIFTY-'
          },
          OPTION_TYPE: {
            in: ['CE', 'PE']
          },
          STRIKE_PRICE: {
            gte: '24650.00000',
            lte: '25050.00000'
          }
        },
        orderBy: [
          { SM_EXPIRY_DATE: 'asc' },
          { STRIKE_PRICE: 'asc' },
          { OPTION_TYPE: 'asc' }
        ],
        take: 20
      });

      nearbyOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.SYMBOL_NAME} (ID: ${option.SECURITY_ID})`);
        console.log(`   Expiry: ${option.SM_EXPIRY_DATE}, Strike: ${option.STRIKE_PRICE}, Type: ${option.OPTION_TYPE}`);
      });

      // Get first expiry date options for testing
      if (expiries.length > 0) {
        const firstExpiry = expiries[0].SM_EXPIRY_DATE;
        console.log(`\n🔍 Options for first expiry (${firstExpiry}):`);
        
        const firstExpiryOptions = await prisma.instruments.findMany({
          where: {
            SYMBOL_NAME: {
              startsWith: 'NIFTY-'
            },
            OPTION_TYPE: {
              in: ['CE', 'PE']
            },
            SM_EXPIRY_DATE: firstExpiry,
            STRIKE_PRICE: {
              gte: '24650.00000',
              lte: '25050.00000'
            }
          },
          orderBy: [
            { STRIKE_PRICE: 'asc' },
            { OPTION_TYPE: 'asc' }
          ]
        });

        console.log(`Found ${firstExpiryOptions.length} options for first expiry around 24850:`);
        firstExpiryOptions.forEach((option, index) => {
          console.log(`${index + 1}. ${option.SYMBOL_NAME} (ID: ${option.SECURITY_ID})`);
          console.log(`   Strike: ${option.STRIKE_PRICE}, Type: ${option.OPTION_TYPE}`);
        });
      }
    }

    // Check NIFTY spot (index)
    console.log('\n📈 Checking for NIFTY spot/index:');
    const niftySpot = await prisma.instruments.findMany({
      where: {
        SYMBOL_NAME: 'NIFTY',
        INSTRUMENT: 'INDEX'
      }
    });

    if (niftySpot.length > 0) {
      console.log('NIFTY spot found:');
      niftySpot.forEach(spot => {
        console.log(`  ${spot.SYMBOL_NAME} (ID: ${spot.SECURITY_ID}) - ${spot.DISPLAY_NAME}`);
      });
    } else {
      console.log('❌ NIFTY spot not found');
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

inspectDatabase();
