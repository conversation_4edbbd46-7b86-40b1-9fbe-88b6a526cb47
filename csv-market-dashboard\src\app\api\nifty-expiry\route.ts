import { NextRequest, NextResponse } from 'next/server';
import { getDhanAPIService } from '../../../services/DhanAPIService';

/**
 * API endpoint to get NIFTY expiry dates
 * GET /api/nifty-expiry
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📅 Fetching NIFTY expiry dates...');

    const dhanAPI = getDhanAPIService();
    
    // NIFTY parameters for Dhan API
    const NIFTY_SCRIP = 13;  // NIFTY security ID
    const NIFTY_SEGMENT = 'IDX_I';  // Index segment

    const expiryDates = await dhanAPI.getExpiryDates(NIFTY_SCRIP, NIFTY_SEGMENT);

    console.log(`✅ Found ${expiryDates.length} NIFTY expiry dates`);

    return NextResponse.json({
      success: true,
      data: {
        underlying: 'NIFTY',
        securityId: NIFTY_SCRIP,
        segment: NIFTY_SEGMENT,
        expiries: expiryDates,
        count: expiryDates.length,
        hasCredentials: dhanAPI.hasCredentials()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching NIFTY expiry dates:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch NIFTY expiry dates',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Handle OPTIONS for CORS
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
