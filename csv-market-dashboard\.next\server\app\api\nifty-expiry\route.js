"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/nifty-expiry/route";
exports.ids = ["app/api/nifty-expiry/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_like_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/nifty-expiry/route.ts */ \"(rsc)/./src/app/api/nifty-expiry/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/nifty-expiry/route\",\n        pathname: \"/api/nifty-expiry\",\n        filename: \"route\",\n        bundlePath: \"app/api/nifty-expiry/route\"\n    },\n    resolvedPagePath: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\app\\\\api\\\\nifty-expiry\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_like_csv_market_dashboard_src_app_api_nifty_expiry_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/nifty-expiry/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/nifty-expiry/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/nifty-expiry/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../services/DhanAPIService */ \"(rsc)/./src/services/DhanAPIService.ts\");\n\n\n/**\n * API endpoint to get NIFTY expiry dates\n * GET /api/nifty-expiry\n */ async function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDCC5 Fetching NIFTY expiry dates...\");\n        const dhanAPI = (0,_services_DhanAPIService__WEBPACK_IMPORTED_MODULE_1__.getDhanAPIService)();\n        // NIFTY parameters for Dhan API\n        const NIFTY_SCRIP = 13; // NIFTY security ID\n        const NIFTY_SEGMENT = \"IDX_I\"; // Index segment\n        const expiryDates = await dhanAPI.getExpiryDates(NIFTY_SCRIP, NIFTY_SEGMENT);\n        console.log(`✅ Found ${expiryDates.length} NIFTY expiry dates`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                underlying: \"NIFTY\",\n                securityId: NIFTY_SCRIP,\n                segment: NIFTY_SEGMENT,\n                expiries: expiryDates,\n                count: expiryDates.length,\n                hasCredentials: dhanAPI.hasCredentials()\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"❌ Error fetching NIFTY expiry dates:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch NIFTY expiry dates\",\n            message: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Handle OPTIONS for CORS\n */ async function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/nifty-expiry/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/DhanAPIService.ts":
/*!****************************************!*\
  !*** ./src/services/DhanAPIService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DhanAPIService: () => (/* binding */ DhanAPIService),\n/* harmony export */   getDhanAPIService: () => (/* binding */ getDhanAPIService)\n/* harmony export */ });\n/**\n * Dhan API Service for REST API calls\n * Handles authentication and API requests to Dhan\n */ class DhanAPIService {\n    constructor(accessToken, clientId){\n        this.baseUrl = \"https://api.dhan.co\";\n        this.expiryCache = new Map();\n        this.cacheTimeout = 5 * 60 * 1000 // 5 minutes cache\n        ;\n        this.accessToken = accessToken || process.env.ACCESS_TOKEN || \"\";\n        this.clientId = clientId || process.env.CLIENT_ID || \"\";\n        if (!this.accessToken || !this.clientId) {\n            console.warn(\"⚠️ Dhan API credentials not provided\");\n        }\n    }\n    /**\n   * Get expiry dates for an underlying instrument\n   */ async getExpiryDates(underlyingScrip, underlyingSeg) {\n        try {\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            // Check cache first\n            const cached = this.expiryCache.get(cacheKey);\n            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {\n                console.log(\"\\uD83D\\uDCCB Using cached expiry dates for\", cacheKey);\n                return cached.data;\n            }\n            if (!this.accessToken || !this.clientId) {\n                console.warn(\"⚠️ No Dhan API credentials - returning mock expiry dates\");\n                const mockData = this.getMockExpiryDates();\n                this.expiryCache.set(cacheKey, {\n                    data: mockData,\n                    timestamp: Date.now()\n                });\n                return mockData;\n            }\n            const requestBody = {\n                UnderlyingScrip: underlyingScrip,\n                UnderlyingSeg: underlyingSeg\n            };\n            console.log(\"\\uD83D\\uDD0D Fetching expiry dates from Dhan API:\", requestBody);\n            const response = await fetch(`${this.baseUrl}/v2/optionchain/expirylist`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"access-token\": this.accessToken,\n                    \"client-id\": this.clientId,\n                    \"Accept\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(`Dhan API error: ${response.status} ${response.statusText}`);\n            }\n            const data = await response.json();\n            console.log(\"✅ Received expiry dates:\", data);\n            const expiryDates = data.data || [];\n            // Cache the result\n            this.expiryCache.set(cacheKey, {\n                data: expiryDates,\n                timestamp: Date.now()\n            });\n            return expiryDates;\n        } catch (error) {\n            console.error(\"❌ Error fetching expiry dates:\", error);\n            // Return mock data as fallback\n            const mockData = this.getMockExpiryDates();\n            const cacheKey = `${underlyingScrip}_${underlyingSeg}`;\n            this.expiryCache.set(cacheKey, {\n                data: mockData,\n                timestamp: Date.now()\n            });\n            return mockData;\n        }\n    }\n    /**\n   * Mock expiry dates for development/fallback\n   */ getMockExpiryDates() {\n        const today = new Date();\n        const expiries = [];\n        // Generate next 6 weekly expiries (Thursdays)\n        for(let i = 0; i < 6; i++){\n            const nextThursday = new Date(today);\n            const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4\n            nextThursday.setDate(today.getDate() + daysUntilThursday + i * 7);\n            expiries.push(nextThursday.toISOString().split(\"T\")[0]);\n        }\n        // Add monthly expiries (last Thursday of month)\n        for(let i = 1; i <= 3; i++){\n            const monthlyExpiry = new Date(today.getFullYear(), today.getMonth() + i, 1);\n            // Find last Thursday of the month\n            monthlyExpiry.setMonth(monthlyExpiry.getMonth() + 1, 0); // Last day of month\n            const lastDay = monthlyExpiry.getDate();\n            const lastDayOfWeek = monthlyExpiry.getDay();\n            const lastThursday = lastDay - (lastDayOfWeek + 3) % 7;\n            monthlyExpiry.setDate(lastThursday);\n            const expiryStr = monthlyExpiry.toISOString().split(\"T\")[0];\n            if (!expiries.includes(expiryStr)) {\n                expiries.push(expiryStr);\n            }\n        }\n        return expiries.sort();\n    }\n    /**\n   * Check if API credentials are available\n   */ hasCredentials() {\n        return !!(this.accessToken && this.clientId);\n    }\n    /**\n   * Get API status\n   */ getStatus() {\n        return {\n            hasCredentials: this.hasCredentials(),\n            baseUrl: this.baseUrl\n        };\n    }\n}\n// Singleton instance\nlet dhanAPIService = null;\nfunction getDhanAPIService() {\n    if (!dhanAPIService) {\n        dhanAPIService = new DhanAPIService();\n    }\n    return dhanAPIService;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvRGhhbkFQSVNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FZTSxNQUFNQTtJQU9YQyxZQUFZQyxXQUFvQixFQUFFQyxRQUFpQixDQUFFO2FBSjdDQyxVQUFrQjthQUNsQkMsY0FBa0UsSUFBSUM7YUFDdEVDLGVBQXVCLElBQUksS0FBSyxLQUFNLGtCQUFrQjs7UUFHOUQsSUFBSSxDQUFDTCxXQUFXLEdBQUdBLGVBQWVNLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWSxJQUFJO1FBQzlELElBQUksQ0FBQ1AsUUFBUSxHQUFHQSxZQUFZSyxRQUFRQyxHQUFHLENBQUNFLFNBQVMsSUFBSTtRQUVyRCxJQUFJLENBQUMsSUFBSSxDQUFDVCxXQUFXLElBQUksQ0FBQyxJQUFJLENBQUNDLFFBQVEsRUFBRTtZQUN2Q1MsUUFBUUMsSUFBSSxDQUFDO1FBQ2Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsZUFBZUMsZUFBdUIsRUFBRUMsYUFBcUIsRUFBcUI7UUFDdEYsSUFBSTtZQUNGLE1BQU1DLFdBQVcsQ0FBQyxFQUFFRixnQkFBZ0IsQ0FBQyxFQUFFQyxjQUFjLENBQUM7WUFFdEQsb0JBQW9CO1lBQ3BCLE1BQU1FLFNBQVMsSUFBSSxDQUFDYixXQUFXLENBQUNjLEdBQUcsQ0FBQ0Y7WUFDcEMsSUFBSUMsVUFBVSxLQUFNRyxHQUFHLEtBQUtILE9BQU9JLFNBQVMsR0FBSSxJQUFJLENBQUNmLFlBQVksRUFBRTtnQkFDakVLLFFBQVFXLEdBQUcsQ0FBQyw4Q0FBb0NOO2dCQUNoRCxPQUFPQyxPQUFPTSxJQUFJO1lBQ3BCO1lBRUEsSUFBSSxDQUFDLElBQUksQ0FBQ3RCLFdBQVcsSUFBSSxDQUFDLElBQUksQ0FBQ0MsUUFBUSxFQUFFO2dCQUN2Q1MsUUFBUUMsSUFBSSxDQUFDO2dCQUNiLE1BQU1ZLFdBQVcsSUFBSSxDQUFDQyxrQkFBa0I7Z0JBQ3hDLElBQUksQ0FBQ3JCLFdBQVcsQ0FBQ3NCLEdBQUcsQ0FBQ1YsVUFBVTtvQkFBRU8sTUFBTUM7b0JBQVVILFdBQVdGLEtBQUtDLEdBQUc7Z0JBQUc7Z0JBQ3ZFLE9BQU9JO1lBQ1Q7WUFFQSxNQUFNRyxjQUFzQztnQkFDMUNDLGlCQUFpQmQ7Z0JBQ2pCZSxlQUFlZDtZQUNqQjtZQUVBSixRQUFRVyxHQUFHLENBQUMscURBQTJDSztZQUV2RCxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQzVCLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQyxFQUFFO2dCQUN4RTZCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixnQkFBZ0IsSUFBSSxDQUFDaEMsV0FBVztvQkFDaEMsYUFBYSxJQUFJLENBQUNDLFFBQVE7b0JBQzFCLFVBQVU7Z0JBQ1o7Z0JBQ0FnQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNUO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDRyxTQUFTTyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLGdCQUFnQixFQUFFUixTQUFTUyxNQUFNLENBQUMsQ0FBQyxFQUFFVCxTQUFTVSxVQUFVLENBQUMsQ0FBQztZQUM3RTtZQUVBLE1BQU1qQixPQUEyQixNQUFNTyxTQUFTVyxJQUFJO1lBQ3BEOUIsUUFBUVcsR0FBRyxDQUFDLDRCQUE0QkM7WUFFeEMsTUFBTW1CLGNBQWNuQixLQUFLQSxJQUFJLElBQUksRUFBRTtZQUVuQyxtQkFBbUI7WUFDbkIsSUFBSSxDQUFDbkIsV0FBVyxDQUFDc0IsR0FBRyxDQUFDVixVQUFVO2dCQUFFTyxNQUFNbUI7Z0JBQWFyQixXQUFXRixLQUFLQyxHQUFHO1lBQUc7WUFFMUUsT0FBT3NCO1FBRVQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RoQyxRQUFRZ0MsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQsK0JBQStCO1lBQy9CLE1BQU1uQixXQUFXLElBQUksQ0FBQ0Msa0JBQWtCO1lBQ3hDLE1BQU1ULFdBQVcsQ0FBQyxFQUFFRixnQkFBZ0IsQ0FBQyxFQUFFQyxjQUFjLENBQUM7WUFDdEQsSUFBSSxDQUFDWCxXQUFXLENBQUNzQixHQUFHLENBQUNWLFVBQVU7Z0JBQUVPLE1BQU1DO2dCQUFVSCxXQUFXRixLQUFLQyxHQUFHO1lBQUc7WUFDdkUsT0FBT0k7UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxxQkFBdUM7UUFDckMsTUFBTW9CLFFBQVEsSUFBSXpCO1FBQ2xCLE1BQU0wQixXQUFxQixFQUFFO1FBRTdCLDhDQUE4QztRQUM5QyxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO1lBQzFCLE1BQU1DLGVBQWUsSUFBSTVCLEtBQUt5QjtZQUM5QixNQUFNSSxvQkFBb0IsQ0FBQyxJQUFJSixNQUFNSyxNQUFNLEtBQUssS0FBSyxLQUFLLEdBQUcsb0JBQW9CO1lBQ2pGRixhQUFhRyxPQUFPLENBQUNOLE1BQU1PLE9BQU8sS0FBS0gsb0JBQXFCRixJQUFJO1lBQ2hFRCxTQUFTTyxJQUFJLENBQUNMLGFBQWFNLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ3hEO1FBRUEsZ0RBQWdEO1FBQ2hELElBQUssSUFBSVIsSUFBSSxHQUFHQSxLQUFLLEdBQUdBLElBQUs7WUFDM0IsTUFBTVMsZ0JBQWdCLElBQUlwQyxLQUFLeUIsTUFBTVksV0FBVyxJQUFJWixNQUFNYSxRQUFRLEtBQUtYLEdBQUc7WUFDMUUsa0NBQWtDO1lBQ2xDUyxjQUFjRyxRQUFRLENBQUNILGNBQWNFLFFBQVEsS0FBSyxHQUFHLElBQUksb0JBQW9CO1lBQzdFLE1BQU1FLFVBQVVKLGNBQWNKLE9BQU87WUFDckMsTUFBTVMsZ0JBQWdCTCxjQUFjTixNQUFNO1lBQzFDLE1BQU1ZLGVBQWVGLFVBQVcsQ0FBQ0MsZ0JBQWdCLEtBQUs7WUFDdERMLGNBQWNMLE9BQU8sQ0FBQ1c7WUFFdEIsTUFBTUMsWUFBWVAsY0FBY0YsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDM0QsSUFBSSxDQUFDVCxTQUFTa0IsUUFBUSxDQUFDRCxZQUFZO2dCQUNqQ2pCLFNBQVNPLElBQUksQ0FBQ1U7WUFDaEI7UUFDRjtRQUVBLE9BQU9qQixTQUFTbUIsSUFBSTtJQUN0QjtJQUVBOztHQUVDLEdBQ0RDLGlCQUEwQjtRQUN4QixPQUFPLENBQUMsQ0FBRSxLQUFJLENBQUNoRSxXQUFXLElBQUksSUFBSSxDQUFDQyxRQUFRO0lBQzdDO0lBRUE7O0dBRUMsR0FDRGdFLFlBQTBEO1FBQ3hELE9BQU87WUFDTEQsZ0JBQWdCLElBQUksQ0FBQ0EsY0FBYztZQUNuQzlELFNBQVMsSUFBSSxDQUFDQSxPQUFPO1FBQ3ZCO0lBQ0Y7QUFDRjtBQUVBLHFCQUFxQjtBQUNyQixJQUFJZ0UsaUJBQXdDO0FBRXJDLFNBQVNDO0lBQ2QsSUFBSSxDQUFDRCxnQkFBZ0I7UUFDbkJBLGlCQUFpQixJQUFJcEU7SUFDdkI7SUFDQSxPQUFPb0U7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL3NlcnZpY2VzL0RoYW5BUElTZXJ2aWNlLnRzP2I2YmQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaGFuIEFQSSBTZXJ2aWNlIGZvciBSRVNUIEFQSSBjYWxsc1xuICogSGFuZGxlcyBhdXRoZW50aWNhdGlvbiBhbmQgQVBJIHJlcXVlc3RzIHRvIERoYW5cbiAqL1xuXG5leHBvcnQgaW50ZXJmYWNlIERoYW5FeHBpcnlSZXNwb25zZSB7XG4gIGRhdGE6IHN0cmluZ1tdOyAvLyBBcnJheSBvZiBleHBpcnkgZGF0ZXMgaW4gWVlZWS1NTS1ERCBmb3JtYXRcbiAgc3RhdHVzOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRGhhbk9wdGlvbkNoYWluUmVxdWVzdCB7XG4gIFVuZGVybHlpbmdTY3JpcDogbnVtYmVyO1xuICBVbmRlcmx5aW5nU2VnOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjbGFzcyBEaGFuQVBJU2VydmljZSB7XG4gIHByaXZhdGUgYWNjZXNzVG9rZW46IHN0cmluZztcbiAgcHJpdmF0ZSBjbGllbnRJZDogc3RyaW5nO1xuICBwcml2YXRlIGJhc2VVcmw6IHN0cmluZyA9ICdodHRwczovL2FwaS5kaGFuLmNvJztcbiAgcHJpdmF0ZSBleHBpcnlDYWNoZTogTWFwPHN0cmluZywgeyBkYXRhOiBzdHJpbmdbXSwgdGltZXN0YW1wOiBudW1iZXIgfT4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgY2FjaGVUaW1lb3V0OiBudW1iZXIgPSA1ICogNjAgKiAxMDAwOyAvLyA1IG1pbnV0ZXMgY2FjaGVcblxuICBjb25zdHJ1Y3RvcihhY2Nlc3NUb2tlbj86IHN0cmluZywgY2xpZW50SWQ/OiBzdHJpbmcpIHtcbiAgICB0aGlzLmFjY2Vzc1Rva2VuID0gYWNjZXNzVG9rZW4gfHwgcHJvY2Vzcy5lbnYuQUNDRVNTX1RPS0VOIHx8ICcnO1xuICAgIHRoaXMuY2xpZW50SWQgPSBjbGllbnRJZCB8fCBwcm9jZXNzLmVudi5DTElFTlRfSUQgfHwgJyc7XG5cbiAgICBpZiAoIXRoaXMuYWNjZXNzVG9rZW4gfHwgIXRoaXMuY2xpZW50SWQpIHtcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIERoYW4gQVBJIGNyZWRlbnRpYWxzIG5vdCBwcm92aWRlZCcpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgZXhwaXJ5IGRhdGVzIGZvciBhbiB1bmRlcmx5aW5nIGluc3RydW1lbnRcbiAgICovXG4gIGFzeW5jIGdldEV4cGlyeURhdGVzKHVuZGVybHlpbmdTY3JpcDogbnVtYmVyLCB1bmRlcmx5aW5nU2VnOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZ1tdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7dW5kZXJseWluZ1NjcmlwfV8ke3VuZGVybHlpbmdTZWd9YDtcblxuICAgICAgLy8gQ2hlY2sgY2FjaGUgZmlyc3RcbiAgICAgIGNvbnN0IGNhY2hlZCA9IHRoaXMuZXhwaXJ5Q2FjaGUuZ2V0KGNhY2hlS2V5KTtcbiAgICAgIGlmIChjYWNoZWQgJiYgKERhdGUubm93KCkgLSBjYWNoZWQudGltZXN0YW1wKSA8IHRoaXMuY2FjaGVUaW1lb3V0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OLIFVzaW5nIGNhY2hlZCBleHBpcnkgZGF0ZXMgZm9yJywgY2FjaGVLZXkpO1xuICAgICAgICByZXR1cm4gY2FjaGVkLmRhdGE7XG4gICAgICB9XG5cbiAgICAgIGlmICghdGhpcy5hY2Nlc3NUb2tlbiB8fCAhdGhpcy5jbGllbnRJZCkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBObyBEaGFuIEFQSSBjcmVkZW50aWFscyAtIHJldHVybmluZyBtb2NrIGV4cGlyeSBkYXRlcycpO1xuICAgICAgICBjb25zdCBtb2NrRGF0YSA9IHRoaXMuZ2V0TW9ja0V4cGlyeURhdGVzKCk7XG4gICAgICAgIHRoaXMuZXhwaXJ5Q2FjaGUuc2V0KGNhY2hlS2V5LCB7IGRhdGE6IG1vY2tEYXRhLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfSk7XG4gICAgICAgIHJldHVybiBtb2NrRGF0YTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVxdWVzdEJvZHk6IERoYW5PcHRpb25DaGFpblJlcXVlc3QgPSB7XG4gICAgICAgIFVuZGVybHlpbmdTY3JpcDogdW5kZXJseWluZ1NjcmlwLFxuICAgICAgICBVbmRlcmx5aW5nU2VnOiB1bmRlcmx5aW5nU2VnXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBGZXRjaGluZyBleHBpcnkgZGF0ZXMgZnJvbSBEaGFuIEFQSTonLCByZXF1ZXN0Qm9keSk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS92Mi9vcHRpb25jaGFpbi9leHBpcnlsaXN0YCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ2FjY2Vzcy10b2tlbic6IHRoaXMuYWNjZXNzVG9rZW4sXG4gICAgICAgICAgJ2NsaWVudC1pZCc6IHRoaXMuY2xpZW50SWQsXG4gICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0Qm9keSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRGhhbiBBUEkgZXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGE6IERoYW5FeHBpcnlSZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgUmVjZWl2ZWQgZXhwaXJ5IGRhdGVzOicsIGRhdGEpO1xuXG4gICAgICBjb25zdCBleHBpcnlEYXRlcyA9IGRhdGEuZGF0YSB8fCBbXTtcblxuICAgICAgLy8gQ2FjaGUgdGhlIHJlc3VsdFxuICAgICAgdGhpcy5leHBpcnlDYWNoZS5zZXQoY2FjaGVLZXksIHsgZGF0YTogZXhwaXJ5RGF0ZXMsIHRpbWVzdGFtcDogRGF0ZS5ub3coKSB9KTtcblxuICAgICAgcmV0dXJuIGV4cGlyeURhdGVzO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBmZXRjaGluZyBleHBpcnkgZGF0ZXM6JywgZXJyb3IpO1xuICAgICAgLy8gUmV0dXJuIG1vY2sgZGF0YSBhcyBmYWxsYmFja1xuICAgICAgY29uc3QgbW9ja0RhdGEgPSB0aGlzLmdldE1vY2tFeHBpcnlEYXRlcygpO1xuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgJHt1bmRlcmx5aW5nU2NyaXB9XyR7dW5kZXJseWluZ1NlZ31gO1xuICAgICAgdGhpcy5leHBpcnlDYWNoZS5zZXQoY2FjaGVLZXksIHsgZGF0YTogbW9ja0RhdGEsIHRpbWVzdGFtcDogRGF0ZS5ub3coKSB9KTtcbiAgICAgIHJldHVybiBtb2NrRGF0YTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTW9jayBleHBpcnkgZGF0ZXMgZm9yIGRldmVsb3BtZW50L2ZhbGxiYWNrXG4gICAqL1xuICBwcml2YXRlIGdldE1vY2tFeHBpcnlEYXRlcygpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IGV4cGlyaWVzOiBzdHJpbmdbXSA9IFtdO1xuXG4gICAgLy8gR2VuZXJhdGUgbmV4dCA2IHdlZWtseSBleHBpcmllcyAoVGh1cnNkYXlzKVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNjsgaSsrKSB7XG4gICAgICBjb25zdCBuZXh0VGh1cnNkYXkgPSBuZXcgRGF0ZSh0b2RheSk7XG4gICAgICBjb25zdCBkYXlzVW50aWxUaHVyc2RheSA9ICg0IC0gdG9kYXkuZ2V0RGF5KCkgKyA3KSAlIDcgfHwgNzsgLy8gVGh1cnNkYXkgaXMgZGF5IDRcbiAgICAgIG5leHRUaHVyc2RheS5zZXREYXRlKHRvZGF5LmdldERhdGUoKSArIGRheXNVbnRpbFRodXJzZGF5ICsgKGkgKiA3KSk7XG4gICAgICBleHBpcmllcy5wdXNoKG5leHRUaHVyc2RheS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0pO1xuICAgIH1cblxuICAgIC8vIEFkZCBtb250aGx5IGV4cGlyaWVzIChsYXN0IFRodXJzZGF5IG9mIG1vbnRoKVxuICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IDM7IGkrKykge1xuICAgICAgY29uc3QgbW9udGhseUV4cGlyeSA9IG5ldyBEYXRlKHRvZGF5LmdldEZ1bGxZZWFyKCksIHRvZGF5LmdldE1vbnRoKCkgKyBpLCAxKTtcbiAgICAgIC8vIEZpbmQgbGFzdCBUaHVyc2RheSBvZiB0aGUgbW9udGhcbiAgICAgIG1vbnRobHlFeHBpcnkuc2V0TW9udGgobW9udGhseUV4cGlyeS5nZXRNb250aCgpICsgMSwgMCk7IC8vIExhc3QgZGF5IG9mIG1vbnRoXG4gICAgICBjb25zdCBsYXN0RGF5ID0gbW9udGhseUV4cGlyeS5nZXREYXRlKCk7XG4gICAgICBjb25zdCBsYXN0RGF5T2ZXZWVrID0gbW9udGhseUV4cGlyeS5nZXREYXkoKTtcbiAgICAgIGNvbnN0IGxhc3RUaHVyc2RheSA9IGxhc3REYXkgLSAoKGxhc3REYXlPZldlZWsgKyAzKSAlIDcpO1xuICAgICAgbW9udGhseUV4cGlyeS5zZXREYXRlKGxhc3RUaHVyc2RheSk7XG4gICAgICBcbiAgICAgIGNvbnN0IGV4cGlyeVN0ciA9IG1vbnRobHlFeHBpcnkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgaWYgKCFleHBpcmllcy5pbmNsdWRlcyhleHBpcnlTdHIpKSB7XG4gICAgICAgIGV4cGlyaWVzLnB1c2goZXhwaXJ5U3RyKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gZXhwaXJpZXMuc29ydCgpO1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIEFQSSBjcmVkZW50aWFscyBhcmUgYXZhaWxhYmxlXG4gICAqL1xuICBoYXNDcmVkZW50aWFscygpOiBib29sZWFuIHtcbiAgICByZXR1cm4gISEodGhpcy5hY2Nlc3NUb2tlbiAmJiB0aGlzLmNsaWVudElkKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgQVBJIHN0YXR1c1xuICAgKi9cbiAgZ2V0U3RhdHVzKCk6IHsgaGFzQ3JlZGVudGlhbHM6IGJvb2xlYW47IGJhc2VVcmw6IHN0cmluZyB9IHtcbiAgICByZXR1cm4ge1xuICAgICAgaGFzQ3JlZGVudGlhbHM6IHRoaXMuaGFzQ3JlZGVudGlhbHMoKSxcbiAgICAgIGJhc2VVcmw6IHRoaXMuYmFzZVVybFxuICAgIH07XG4gIH1cbn1cblxuLy8gU2luZ2xldG9uIGluc3RhbmNlXG5sZXQgZGhhbkFQSVNlcnZpY2U6IERoYW5BUElTZXJ2aWNlIHwgbnVsbCA9IG51bGw7XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXREaGFuQVBJU2VydmljZSgpOiBEaGFuQVBJU2VydmljZSB7XG4gIGlmICghZGhhbkFQSVNlcnZpY2UpIHtcbiAgICBkaGFuQVBJU2VydmljZSA9IG5ldyBEaGFuQVBJU2VydmljZSgpO1xuICB9XG4gIHJldHVybiBkaGFuQVBJU2VydmljZTtcbn1cbiJdLCJuYW1lcyI6WyJEaGFuQVBJU2VydmljZSIsImNvbnN0cnVjdG9yIiwiYWNjZXNzVG9rZW4iLCJjbGllbnRJZCIsImJhc2VVcmwiLCJleHBpcnlDYWNoZSIsIk1hcCIsImNhY2hlVGltZW91dCIsInByb2Nlc3MiLCJlbnYiLCJBQ0NFU1NfVE9LRU4iLCJDTElFTlRfSUQiLCJjb25zb2xlIiwid2FybiIsImdldEV4cGlyeURhdGVzIiwidW5kZXJseWluZ1NjcmlwIiwidW5kZXJseWluZ1NlZyIsImNhY2hlS2V5IiwiY2FjaGVkIiwiZ2V0IiwiRGF0ZSIsIm5vdyIsInRpbWVzdGFtcCIsImxvZyIsImRhdGEiLCJtb2NrRGF0YSIsImdldE1vY2tFeHBpcnlEYXRlcyIsInNldCIsInJlcXVlc3RCb2R5IiwiVW5kZXJseWluZ1NjcmlwIiwiVW5kZXJseWluZ1NlZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsIkVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImpzb24iLCJleHBpcnlEYXRlcyIsImVycm9yIiwidG9kYXkiLCJleHBpcmllcyIsImkiLCJuZXh0VGh1cnNkYXkiLCJkYXlzVW50aWxUaHVyc2RheSIsImdldERheSIsInNldERhdGUiLCJnZXREYXRlIiwicHVzaCIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJtb250aGx5RXhwaXJ5IiwiZ2V0RnVsbFllYXIiLCJnZXRNb250aCIsInNldE1vbnRoIiwibGFzdERheSIsImxhc3REYXlPZldlZWsiLCJsYXN0VGh1cnNkYXkiLCJleHBpcnlTdHIiLCJpbmNsdWRlcyIsInNvcnQiLCJoYXNDcmVkZW50aWFscyIsImdldFN0YXR1cyIsImRoYW5BUElTZXJ2aWNlIiwiZ2V0RGhhbkFQSVNlcnZpY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/services/DhanAPIService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnifty-expiry%2Froute&page=%2Fapi%2Fnifty-expiry%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnifty-expiry%2Froute.ts&appDir=D%3A%5Clike%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clike%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();