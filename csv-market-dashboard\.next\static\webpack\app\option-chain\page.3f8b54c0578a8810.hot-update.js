"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        console.log(\"\\uD83D\\uDCCA Total market data entries:\", marketData.size);\n        // Debug: Show sample market data entries\n        const sampleEntries = Array.from(marketData.entries()).slice(0, 10);\n        console.log(\"\\uD83D\\uDCCB Sample market data entries:\", sampleEntries.map((param)=>{\n            let [id, data] = param;\n            return {\n                securityId: id,\n                symbol: data.symbol,\n                expiryDate: data.expiryDate,\n                optionType: data.optionType,\n                strikePrice: data.strikePrice\n            };\n        }));\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        console.log(\"\\uD83C\\uDFAF Generated strikes:\", strikes);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        console.log(\"\\uD83D\\uDCC8 Option chain rows built:\", rows.length, \"rows\");\n        console.log(\"✅ Found options:\", rows.filter((r)=>r.call || r.put).length, \"strikes with data\");\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        console.log(\"[SEARCH] Looking for \".concat(optionType, \" \").concat(strike, \" with expiry \").concat(selectedExpiry));\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n        // Show available NIFTY options for debugging\n        const niftyOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\")).slice(0, 10);\n        console.log(\"[DEBUG] Available NIFTY options (\".concat(niftyOptions.length, \" total):\"), niftyOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \", Strike: \").concat(opt.strikePrice, \", Type: \").concat(opt.optionType, \")\")));\n        // Show specific type options\n        const typeOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n        console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), typeOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 279,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_call7, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6, _row_put7;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-500\"),\n                                        children: ((_row_call7 = row.call) === null || _row_call7 === void 0 ? void 0 : _row_call7.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-500\"),\n                                        children: ((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change)),\n                                        children: ((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put7 = row.put) === null || _row_put7 === void 0 ? void 0 : _row_put7.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"3MYzdkz2i0N1g/MoBahr6e4MTpU=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09wdGlvbkNoYWluLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEM7QUFPN0IsU0FBU0UsWUFBWSxLQUFnQztRQUFoQyxFQUFFQyxVQUFVLEVBQW9CLEdBQWhDOztJQUNsQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0wsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQ00sZ0JBQWdCQyxrQkFBa0IsR0FBR1AsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUF5QjtJQUN2RSxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNjLGdCQUFnQkMsa0JBQWtCLEdBQUdmLCtDQUFRQSxDQUFTO0lBRTdELDZGQUE2RjtJQUM3RkMsZ0RBQVNBLENBQUM7UUFDUixvR0FBb0c7UUFDcEcsTUFBTWUsWUFBWWIsV0FBV2MsR0FBRyxDQUFDO1FBQ2pDLElBQUlELGFBQWFBLFVBQVVFLEdBQUcsR0FBRyxHQUFHO1lBQ2xDSCxrQkFBa0JDLFVBQVVFLEdBQUc7WUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxpREFBdUNKLFVBQVVFLEdBQUcsRUFBRTtZQUNsRTtRQUNGO1FBRUEsMkVBQTJFO1FBQzNFLEtBQUssTUFBTSxDQUFDRyxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUsSUFBSUgsS0FBS0ksTUFBTSxLQUFLLFdBQVdKLEtBQUtKLEdBQUcsR0FBRyxHQUFHO2dCQUMzQ0gsa0JBQWtCTyxLQUFLSixHQUFHO2dCQUMxQkMsUUFBUUMsR0FBRyxDQUFDLCtDQUFxQ0UsS0FBS0osR0FBRyxFQUFFLGtCQUFrQkcsWUFBWSxhQUFhQyxLQUFLSyxRQUFRO2dCQUNuSDtZQUNGO1FBQ0Y7UUFFQSx1RkFBdUY7UUFDdkYsSUFBSWIsbUJBQW1CLEdBQUc7WUFDeEIsTUFBTWMsWUFBWTtZQUNsQmIsa0JBQWtCYTtZQUNsQlQsUUFBUVUsSUFBSSxDQUFDLDBDQUEwQ0QsV0FBVztRQUNwRTtJQUNGLEdBQUc7UUFBQ3pCO0tBQVc7SUFFZix3Q0FBd0M7SUFDeENGLGdEQUFTQSxDQUFDO1FBQ1I2QjtJQUNGLEdBQUcsRUFBRTtJQUVMLDZDQUE2QztJQUM3QzdCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSUssa0JBQWtCUSxpQkFBaUIsR0FBRztZQUN4Q2lCO1FBQ0Y7SUFDRixHQUFHO1FBQUN6QjtRQUFnQlE7UUFBZ0JYO0tBQVc7SUFFL0MsTUFBTTJCLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0ZuQixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxNQUFNbUIsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0saUNBQXFELE9BQXBCSCxTQUFTSSxVQUFVO1lBQ3RFO1lBRUEsTUFBTUMsU0FBUyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2xDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJsQyxjQUFjZ0MsT0FBT2YsSUFBSTtnQkFDekIsMkJBQTJCO2dCQUMzQixJQUFJZSxPQUFPZixJQUFJLENBQUNrQixRQUFRLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUNuQ2xDLGtCQUFrQjhCLE9BQU9mLElBQUksQ0FBQ2tCLFFBQVEsQ0FBQyxFQUFFO2dCQUMzQztZQUNGLE9BQU87Z0JBQ0wsTUFBTSxJQUFJTCxNQUFNRSxPQUFPSyxPQUFPLElBQUk7WUFDcEM7UUFDRixFQUFFLE9BQU9DLEtBQUs7WUFDWnhCLFFBQVFQLEtBQUssQ0FBQyxnQ0FBZ0MrQjtZQUM5QzlCLFNBQVM4QixlQUFlUixRQUFRUSxJQUFJRCxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSL0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNb0IsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ3pCLGtCQUFrQlEsa0JBQWtCLEdBQUc7UUFFNUNLLFFBQVFDLEdBQUcsQ0FBQyxrREFBd0NkLGdCQUFnQixTQUFTUTtRQUM3RUssUUFBUUMsR0FBRyxDQUFDLDJDQUFpQ2pCLFdBQVd5QyxJQUFJO1FBRTVELHlDQUF5QztRQUN6QyxNQUFNQyxnQkFBZ0J0QixNQUFNQyxJQUFJLENBQUNyQixXQUFXc0IsT0FBTyxJQUFJcUIsS0FBSyxDQUFDLEdBQUc7UUFDaEUzQixRQUFRQyxHQUFHLENBQUMsNENBQWtDeUIsY0FBY0UsR0FBRyxDQUFDO2dCQUFDLENBQUNDLElBQUkxQixLQUFLO21CQUFNO2dCQUMvRUQsWUFBWTJCO2dCQUNadEIsUUFBUUosS0FBS0ksTUFBTTtnQkFDbkJ1QixZQUFZM0IsS0FBSzJCLFVBQVU7Z0JBQzNCQyxZQUFZNUIsS0FBSzRCLFVBQVU7Z0JBQzNCQyxhQUFhN0IsS0FBSzZCLFdBQVc7WUFDL0I7O1FBRUEsMkNBQTJDO1FBQzNDLE1BQU1DLFVBQVVDLGdCQUFnQnZDO1FBQ2hDSyxRQUFRQyxHQUFHLENBQUMsbUNBQXlCZ0M7UUFFckMsMEJBQTBCO1FBQzFCLE1BQU1FLE9BQXlCRixRQUFRTCxHQUFHLENBQUNRLENBQUFBO1lBQ3pDLE1BQU1DLFdBQVdDLGVBQWVGLFFBQVE7WUFDeEMsTUFBTUcsVUFBVUQsZUFBZUYsUUFBUTtZQUV2QyxPQUFPO2dCQUNMSixhQUFhSTtnQkFDYkksTUFBTUg7Z0JBQ05JLEtBQUtGO1lBQ1A7UUFDRjtRQUVBdkMsUUFBUUMsR0FBRyxDQUFDLHlDQUErQmtDLEtBQUtiLE1BQU0sRUFBRTtRQUN4RHRCLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JrQyxLQUFLTyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVILElBQUksSUFBSUcsRUFBRUYsR0FBRyxFQUFFbkIsTUFBTSxFQUFFO1FBRTFFaEMsZUFBZTtZQUNic0QsWUFBWTtZQUNaQyxXQUFXbEQ7WUFDWG1ELFFBQVEzRDtZQUNSZ0Q7WUFDQVksV0FBV0MsS0FBS0MsR0FBRztRQUNyQjtJQUNGO0lBRUEsTUFBTWYsa0JBQWtCLENBQUNXO1FBQ3ZCLDRFQUE0RTtRQUM1RSxNQUFNSyxtQkFBbUIsSUFBSUM7UUFFN0IsS0FBSyxNQUFNLENBQUNqRCxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUsSUFBSUgsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDLFlBQ3JCakQsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDQyxzQkFBc0JsRSxrQkFBa0I7Z0JBRS9ELDREQUE0RDtnQkFDNUQsTUFBTW1FLGNBQWNuRCxLQUFLSSxNQUFNLENBQUNnRCxLQUFLLENBQUM7Z0JBQ3RDLElBQUlELFlBQVloQyxNQUFNLElBQUksR0FBRztvQkFDM0IsTUFBTWMsU0FBU29CLFdBQVdGLFdBQVcsQ0FBQyxFQUFFO29CQUN4QyxJQUFJLENBQUNHLE1BQU1yQixTQUFTO3dCQUNsQmMsaUJBQWlCUSxHQUFHLENBQUN0QjtvQkFDdkI7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSWMsaUJBQWlCekIsSUFBSSxLQUFLLEdBQUc7WUFDL0IsK0NBQStDO1lBQy9DLE1BQU1RLFVBQW9CLEVBQUU7WUFDNUIsTUFBTTBCLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ2hCLFlBQVksTUFBTTtZQUNoRCxJQUFLLElBQUlpQixJQUFJLENBQUMsSUFBSUEsS0FBSyxJQUFJQSxJQUFLO2dCQUM5QjdCLFFBQVE4QixJQUFJLENBQUNKLGFBQWNHLElBQUk7WUFDakM7WUFDQSxPQUFPN0IsUUFBUStCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUNwQztRQUVBLDBCQUEwQjtRQUMxQixNQUFNQyxhQUFhL0QsTUFBTUMsSUFBSSxDQUFDNkMsa0JBQWtCYyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7UUFFbkUsMENBQTBDO1FBQzFDLE1BQU1FLFlBQVlELFdBQVdFLE1BQU0sQ0FBQyxDQUFDQyxNQUFNQyxPQUN6Q1gsS0FBS1ksR0FBRyxDQUFDRCxPQUFPMUIsYUFBYWUsS0FBS1ksR0FBRyxDQUFDRixPQUFPekIsYUFBYTBCLE9BQU9EO1FBR25FdEUsUUFBUUMsR0FBRyxDQUFDLGdEQUEwRDRDLE9BQXBCdUIsV0FBVSxZQUFvQixPQUFWdkIsV0FBVTtRQUNoRjdDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBb0QsT0FBbEJrRSxXQUFXN0MsTUFBTTtRQUUvRCwrRUFBK0U7UUFDL0UsTUFBTW1ELFdBQVdOLFdBQVdPLE9BQU8sQ0FBQ047UUFDcEMsTUFBTU8sa0JBQWtCO2VBQ25CUixXQUFXeEMsS0FBSyxDQUFDaUMsS0FBS2dCLEdBQUcsQ0FBQyxHQUFHSCxXQUFXLEtBQUtBO1lBQ2hETDtlQUNHRCxXQUFXeEMsS0FBSyxDQUFDOEMsV0FBVyxHQUFHQSxXQUFXO1NBQzlDO1FBRUR6RSxRQUFRQyxHQUFHLENBQUMsdUJBQThDLE9BQXZCMEUsZ0JBQWdCckQsTUFBTSxFQUFDLHlCQUF1QnFEO1FBRWpGLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNckMsaUJBQWlCLENBQUNGLFFBQWdCTDtRQUN0Qy9CLFFBQVFDLEdBQUcsQ0FBQyx3QkFBc0NtQyxPQUFkTCxZQUFXLEtBQXlCNUMsT0FBdEJpRCxRQUFPLGlCQUE4QixPQUFmakQ7UUFFeEUscUVBQXFFO1FBQ3JFLEtBQUssTUFBTSxDQUFDZSxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUseURBQXlEO1lBQ3pELElBQUlILEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxhQUFjLDRDQUE0QztZQUMvRWpELEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxJQUFjckIsT0FBVkssUUFBTyxLQUFjLE9BQVhMLGdCQUNuQzVCLEtBQUsyQixVQUFVLEtBQUszQyxnQkFBZ0I7b0JBZ0IvQmdCLG9CQUFBQSxtQkFDQUEscUJBQUFBLG9CQUNHQSxxQkFBQUEsb0JBQ0FBLHFCQUFBQTtnQkFqQlZILFFBQVFDLEdBQUcsQ0FBQyxvQkFBa0NtQyxPQUFkTCxZQUFXLEtBQWM1QixPQUFYaUMsUUFBTyxNQUE0QmpDLE9BQXhCQSxLQUFLSSxNQUFNLEVBQUMsY0FBNEIsT0FBaEJKLEtBQUsyQixVQUFVLEVBQUM7Z0JBRWpHLE9BQU87b0JBQ0w1QjtvQkFDQUssUUFBUUosS0FBS0ksTUFBTTtvQkFDbkJDLFVBQVVMLEtBQUtLLFFBQVE7b0JBQ3ZCd0IsYUFBYUk7b0JBQ2JMO29CQUNBRCxZQUFZM0M7b0JBQ1pZLEtBQUtJLEtBQUtKLEdBQUcsSUFBSTtvQkFDakI4RSxRQUFRMUUsS0FBSzBFLE1BQU0sSUFBSTtvQkFDdkJDLGVBQWUzRSxLQUFLMkUsYUFBYSxJQUFJO29CQUNyQ0MsUUFBUTVFLEtBQUs0RSxNQUFNLElBQUk7b0JBQ3ZCQyxjQUFjN0UsS0FBSzZFLFlBQVk7b0JBQy9CQyxLQUFLOUUsRUFBQUEsb0JBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UseUNBQUFBLHFCQUFBQSxpQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSx5Q0FBQUEsbUJBQXVCZ0YsUUFBUSxLQUFJaEYsS0FBSzhFLEdBQUc7b0JBQ2hERyxLQUFLakYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCa0YsUUFBUSxLQUFJbEYsS0FBS2lGLEdBQUc7b0JBQ2hERSxRQUFRbkYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCbUYsTUFBTSxLQUFJbkYsS0FBS21GLE1BQU07b0JBQ3BEQyxRQUFRcEYsRUFBQUEscUJBQUFBLEtBQUsrRSxXQUFXLGNBQWhCL0UsMENBQUFBLHNCQUFBQSxrQkFBa0IsQ0FBQyxFQUFFLGNBQXJCQSwwQ0FBQUEsb0JBQXVCb0YsTUFBTSxLQUFJcEYsS0FBS29GLE1BQU07b0JBQ3BEQyxNQUFNckYsS0FBS3FGLElBQUk7b0JBQ2ZDLEtBQUt0RixLQUFLc0YsR0FBRztvQkFDYkMsTUFBTXZGLEtBQUt1RixJQUFJO29CQUNmQyxPQUFPeEYsS0FBS3dGLEtBQUs7b0JBQ2pCNUMsV0FBVzVDLEtBQUs0QyxTQUFTO2dCQUMzQjtZQUNGO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMvQyxRQUFRQyxHQUFHLENBQUMsd0JBQXNDbUMsT0FBZEwsWUFBVyxLQUF3QjVDLE9BQXJCaUQsUUFBTyxnQkFBNkIsT0FBZmpEO1FBRXZFLDZDQUE2QztRQUM3QyxNQUFNeUcsZUFBZXhGLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVc2RyxNQUFNLElBQzlDbkQsTUFBTSxDQUFDdkMsQ0FBQUEsT0FBUUEsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDLFdBQ3BDekIsS0FBSyxDQUFDLEdBQUc7UUFFWjNCLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBd0QsT0FBcEIyRixhQUFhdEUsTUFBTSxFQUFDLGFBQVdzRSxhQUFhaEUsR0FBRyxDQUFDa0UsQ0FBQUEsTUFDOUYsR0FBMEJBLE9BQXZCQSxJQUFJdkYsTUFBTSxFQUFDLGNBQXVDdUYsT0FBM0JBLElBQUloRSxVQUFVLEVBQUMsY0FBc0NnRSxPQUExQkEsSUFBSTlELFdBQVcsRUFBQyxZQUF5QixPQUFmOEQsSUFBSS9ELFVBQVUsRUFBQztRQUdoRyw2QkFBNkI7UUFDN0IsTUFBTWdFLGNBQWMzRixNQUFNQyxJQUFJLENBQUNyQixXQUFXNkcsTUFBTSxJQUM3Q25ELE1BQU0sQ0FBQ3ZDLENBQUFBLE9BQVFBLEtBQUtJLE1BQU0sQ0FBQzZDLFFBQVEsQ0FBQyxhQUFhakQsS0FBS0ksTUFBTSxDQUFDNkMsUUFBUSxDQUFDLElBQWUsT0FBWHJCLGNBQzFFSixLQUFLLENBQUMsR0FBRztRQUVaM0IsUUFBUUMsR0FBRyxDQUFDLHFCQUFnQyxPQUFYOEIsWUFBVyxjQUFZZ0UsWUFBWW5FLEdBQUcsQ0FBQ2tFLENBQUFBLE1BQ3RFLEdBQTBCQSxPQUF2QkEsSUFBSXZGLE1BQU0sRUFBQyxjQUEyQixPQUFmdUYsSUFBSWhFLFVBQVUsRUFBQztRQUczQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNdUIsd0JBQXdCLENBQUNQO1FBQzdCLCtEQUErRDtRQUMvRCxNQUFNa0QsT0FBTyxJQUFJaEQsS0FBS0Y7UUFDdEIsTUFBTW1ELFNBQVM7WUFBQztZQUFPO1lBQU87WUFBTztZQUFPO1lBQU87WUFDcEM7WUFBTztZQUFPO1lBQU87WUFBTztZQUFPO1NBQU07UUFDeEQsT0FBTyxHQUE2QkQsT0FBMUJDLE1BQU0sQ0FBQ0QsS0FBS0UsUUFBUSxHQUFHLEVBQXNCLE9BQW5CRixLQUFLRyxXQUFXO0lBQ3REO0lBRUEsTUFBTUMsY0FBYyxDQUFDQztRQUNuQixJQUFJLENBQUNBLFNBQVNBLFNBQVMsR0FBRyxPQUFPO1FBQ2pDLE9BQU8sSUFBcUIsT0FBakJBLE1BQU1DLE9BQU8sQ0FBQztJQUMzQjtJQUVBLE1BQU1DLGVBQWUsQ0FBQ0M7UUFDcEIsSUFBSSxDQUFDQSxLQUFLLE9BQU87UUFDakIsT0FBT0EsSUFBSUMsY0FBYztJQUMzQjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDN0I7UUFDdEIsSUFBSSxDQUFDQSxRQUFRLE9BQU87UUFDcEIsT0FBT0EsU0FBUyxJQUFJLG1CQUFtQkEsU0FBUyxJQUFJLGlCQUFpQjtJQUN2RTtJQUVBLElBQUl0RixTQUFTO1FBQ1gscUJBQ0UsOERBQUNvSDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxJQUFJbkgsT0FBTztRQUNULHFCQUNFLDhEQUFDa0g7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNFO29CQUFHRixXQUFVOzhCQUEyQjs7Ozs7OzhCQUN6Qyw4REFBQ0M7b0JBQUVELFdBQVU7OEJBQTZCbkg7Ozs7Ozs4QkFDMUMsOERBQUNzSDtvQkFDQ0MsU0FBU3JHO29CQUNUaUcsV0FBVTs4QkFDWDs7Ozs7Ozs7Ozs7O0lBS1A7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7b0NBQUdMLFdBQVU7OENBQW1DOzs7Ozs7OENBR2pELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFPSCxXQUFVO3NEQUFvRTs7Ozs7O3NEQUd0Riw4REFBQ0c7NENBQU9ILFdBQVU7c0RBQTZFOzs7Ozs7c0RBRy9GLDhEQUFDRzs0Q0FBT0gsV0FBVTtzREFBNkU7Ozs7OztzREFHL0YsOERBQUNHOzRDQUFPSCxXQUFVO3NEQUE2RTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9uRyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FHdkMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ007NENBQUtOLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3hDLDhEQUFDTTs0Q0FBS04sV0FBVTtzREFDYlIsWUFBWXpHOzs7Ozs7d0NBRWRBLG1CQUFtQix1QkFDbEIsOERBQUN1SDs0Q0FBS04sV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUWpEM0gsNEJBQ0MsOERBQUMwSDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaM0gsV0FBV29DLFFBQVEsQ0FBQ00sS0FBSyxDQUFDLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUNrQixRQUFRcUU7NEJBQzdDLE1BQU1uQixPQUFPLElBQUloRCxLQUFLRjs0QkFDdEIsTUFBTXNFLGFBQWF0RSxXQUFXM0Q7NEJBQzlCLE1BQU1rSSxVQUFVckIsS0FBS3NCLFlBQVksT0FBTyxJQUFJdEUsT0FBT3NFLFlBQVk7NEJBQy9ELE1BQU1DLGdCQUFnQjNELEtBQUtZLEdBQUcsQ0FBQ3dCLEtBQUt3QixPQUFPLEtBQUssSUFBSXhFLE9BQU93RSxPQUFPLE1BQU0sSUFBSSxLQUFLLEtBQUssS0FBSzs0QkFFM0YsMkJBQTJCOzRCQUMzQixNQUFNQyxjQUFjLElBQUl6RSxPQUFPbUQsV0FBVzs0QkFDMUMsTUFBTXVCLGFBQWExQixLQUFLRyxXQUFXOzRCQUNuQyxNQUFNd0IsYUFBYTNCLEtBQUs0QixrQkFBa0IsQ0FBQyxTQUFTO2dDQUFFQyxPQUFPOzRCQUFROzRCQUNyRSxNQUFNQyxNQUFNOUIsS0FBSytCLE9BQU87NEJBRXhCLDJDQUEyQzs0QkFDM0MsTUFBTUMsV0FBV04sZUFBZUQ7NEJBRWhDLHFCQUNFLDhEQUFDVjtnQ0FFQ0MsU0FBUyxJQUFNNUgsa0JBQWtCMEQ7Z0NBQ2pDOEQsV0FBVyx3RUFRVixPQVBDUSxhQUNJLHFDQUNBQyxVQUNBLCtDQUNBRSxnQkFDQSxnRUFDQTswQ0FHTiw0RUFBQ1o7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Z0RBQ1plO2dEQUFZSyxZQUFZLElBQW9DLE9BQWhDTixXQUFXTyxRQUFRLEdBQUd0RyxLQUFLLENBQUMsQ0FBQzs7Ozs7OztzREFFNUQsOERBQUNnRjs0Q0FBSUMsV0FBVTtzREFDWmtCOzs7Ozs7Ozs7Ozs7K0JBakJBaEY7Ozs7O3dCQXNCWDs7Ozs7O29CQXpYWixLQTZYb0Msa0JBQ3hCLDhEQUFDNkQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUFJLDhEQUFDdUI7c0RBQU87Ozs7Ozt3Q0FBeUI7d0NBQUUvSTs7Ozs7Ozs4Q0FDeEMsOERBQUN3SDs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUFvQjt3Q0FBR3ZJO3dDQUFlO3dDQUFFQSxtQkFBbUIsUUFBUSxXQUFXOzs7Ozs7OzhDQUMzRiw4REFBQ2dIOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQTJCO3dDQUFFN0ksQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhOEMsSUFBSSxDQUFDYixNQUFNLEtBQUk7Ozs7Ozs7OENBQ3RFLDhEQUFDcUY7O3NEQUFJLDhEQUFDdUI7c0RBQU87Ozs7Ozt3Q0FBMEI7d0NBQUVsSixXQUFXeUMsSUFBSTt3Q0FBQzs7Ozs7Ozs4Q0FDekQsOERBQUNrRjs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUF1Qjt3Q0FBRTlILE1BQU1DLElBQUksQ0FBQ3JCLFdBQVc2RyxNQUFNLElBQUluRCxNQUFNLENBQUN5RixDQUFBQSxJQUFLQSxFQUFFNUgsTUFBTSxDQUFDNkMsUUFBUSxDQUFDLFdBQVc5QixNQUFNO3dDQUFDOzs7Ozs7OzhDQUN0SCw4REFBQ3FGOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQTZCO3dDQUFFOUgsTUFBTUMsSUFBSSxDQUFDckIsV0FBVzZHLE1BQU0sSUFBSW5ELE1BQU0sQ0FBQ3lGLENBQUFBLElBQUtBLEVBQUU1SCxNQUFNLENBQUM2QyxRQUFRLENBQUMsYUFBYStFLEVBQUVyRyxVQUFVLEVBQUVSLE1BQU07d0NBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFySmpDLDZCQUNDLDhEQUFDc0g7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTTtvQ0FBS04sV0FBVTs4Q0FBMkI7Ozs7Ozs7Ozs7OzBDQUc3Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNNO29DQUFLTixXQUFVOzhDQUFZOzs7Ozs7Ozs7OzswQ0FHOUIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTTtvQ0FBS04sV0FBVTs4Q0FBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUs3Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBd0Q7Ozs7OzswQ0FHdkUsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUFnRTs7Ozs7OzBDQUcvRSw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7Ozs7Ozs7a0NBSWhELDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWnZILFlBQVk4QyxJQUFJLENBQUNQLEdBQUcsQ0FBQyxDQUFDd0csS0FBS2pCO2dDQWNOaUIsV0FHQUEsWUFHREEsWUFHQUEsWUFFb0VBLFlBQ2hGQSxZQVlZQSxZQUtaQSxZQWNBQSxVQUtZQSxXQUVvRUEsV0FDaEZBLFdBVVlBLFdBR0FBLFdBR0NBLFdBR0FBOzRCQW5GcEIsTUFBTUMsUUFBUXpFLEtBQUtZLEdBQUcsQ0FBQzRELElBQUlwRyxXQUFXLEdBQUdyQyxtQkFBbUI7NEJBQzVELE1BQU0ySSxhQUFhRixJQUFJcEcsV0FBVyxHQUFHckM7NEJBQ3JDLE1BQU00SSxZQUFZSCxJQUFJcEcsV0FBVyxHQUFHckM7NEJBRXBDLHFCQUNFLDhEQUFDZ0g7Z0NBRUNDLFdBQVcsMkNBRVYsT0FEQ3lCLFFBQVEsaUJBQWlCbEIsUUFBUSxNQUFNLElBQUksYUFBYTs7a0RBSTFELDhEQUFDUjt3Q0FBSUMsV0FBVyx3Q0FBb0csT0FBNUQwQixhQUFhLCtCQUErQjtrREFDakcvQixjQUFhNkIsWUFBQUEsSUFBSTVGLElBQUksY0FBUjRGLGdDQUFBQSxVQUFVcEQsWUFBWTs7Ozs7O2tEQUV0Qyw4REFBQzJCO3dDQUFJQyxXQUFXLHdDQUFvRyxPQUE1RDBCLGFBQWEsK0JBQStCO2tEQUNqRy9CLGNBQWE2QixhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVVyRCxNQUFNOzs7Ozs7a0RBRWhDLDhEQUFDNEI7d0NBQUlDLFdBQVU7a0RBQ1pSLGFBQVlnQyxhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVVuRCxHQUFHOzs7Ozs7a0RBRTVCLDhEQUFDMEI7d0NBQUlDLFdBQVU7a0RBQ1pSLGFBQVlnQyxhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVVoRCxHQUFHOzs7Ozs7a0RBRTVCLDhEQUFDdUI7d0NBQUlDLFdBQVcsb0RBQXFGLE9BQWpDRixnQkFBZTBCLGFBQUFBLElBQUk1RixJQUFJLGNBQVI0RixpQ0FBQUEsV0FBVXZELE1BQU07a0RBQ2hHdUQsRUFBQUEsYUFBQUEsSUFBSTVGLElBQUksY0FBUjRGLGlDQUFBQSxXQUFVdkQsTUFBTSxrQkFDZjs7Z0RBQ0d1RCxJQUFJNUYsSUFBSSxDQUFDcUMsTUFBTSxHQUFHLElBQUksTUFBTTtnREFBSXVELElBQUk1RixJQUFJLENBQUNxQyxNQUFNLENBQUN5QixPQUFPLENBQUM7OERBQ3pELDhEQUFDSztvREFBSUMsV0FBVTs7d0RBQVU7d0RBQ3JCd0IsSUFBSTVGLElBQUksQ0FBQ3NDLGFBQWEsR0FBRyxJQUFJLE1BQU07d0RBQUlzRCxJQUFJNUYsSUFBSSxDQUFDc0MsYUFBYSxDQUFDd0IsT0FBTyxDQUFDO3dEQUFHOzs7Ozs7OzsyREFHN0U7Ozs7OztrREFFTiw4REFBQ0s7d0NBQUlDLFdBQVcsa0RBRWYsT0FEQzBCLGFBQWEsbUJBQW1CO2tEQUUvQmxDLGFBQVlnQyxhQUFBQSxJQUFJNUYsSUFBSSxjQUFSNEYsaUNBQUFBLFdBQVVySSxHQUFHOzs7Ozs7a0RBRTVCLDhEQUFDNEc7d0NBQUlDLFdBQVcsMkVBRWYsT0FEQzBCLGFBQWEsbUJBQW1CO2tEQUUvQkYsRUFBQUEsYUFBQUEsSUFBSTVGLElBQUksY0FBUjRGLGlDQUFBQSxXQUFVbEksVUFBVSxLQUFJOzs7Ozs7a0RBSTNCLDhEQUFDeUc7d0NBQUlDLFdBQVcseUVBRWYsT0FEQ3lCLFFBQVEsa0NBQWtDO2tEQUV6Q0QsSUFBSXBHLFdBQVc7Ozs7OztrREFJbEIsOERBQUMyRTt3Q0FBSUMsV0FBVyxrREFFZixPQURDMkIsWUFBWSxpQkFBaUI7a0RBRTVCSCxFQUFBQSxXQUFBQSxJQUFJM0YsR0FBRyxjQUFQMkYsK0JBQUFBLFNBQVNsSSxVQUFVLEtBQUk7Ozs7OztrREFFMUIsOERBQUN5Rzt3Q0FBSUMsV0FBVyxrREFFZixPQURDMkIsWUFBWSxpQkFBaUI7a0RBRTVCbkMsYUFBWWdDLFlBQUFBLElBQUkzRixHQUFHLGNBQVAyRixnQ0FBQUEsVUFBU3JJLEdBQUc7Ozs7OztrREFFM0IsOERBQUM0Rzt3Q0FBSUMsV0FBVyxvREFBb0YsT0FBaENGLGdCQUFlMEIsWUFBQUEsSUFBSTNGLEdBQUcsY0FBUDJGLGdDQUFBQSxVQUFTdkQsTUFBTTtrREFDL0Z1RCxFQUFBQSxZQUFBQSxJQUFJM0YsR0FBRyxjQUFQMkYsZ0NBQUFBLFVBQVN2RCxNQUFNLGtCQUNkOztnREFDR3VELElBQUkzRixHQUFHLENBQUNvQyxNQUFNLEdBQUcsSUFBSSxNQUFNO2dEQUFJdUQsSUFBSTNGLEdBQUcsQ0FBQ29DLE1BQU0sQ0FBQ3lCLE9BQU8sQ0FBQzs4REFDdkQsOERBQUNLO29EQUFJQyxXQUFVOzt3REFBVTt3REFDckJ3QixJQUFJM0YsR0FBRyxDQUFDcUMsYUFBYSxHQUFHLElBQUksTUFBTTt3REFBSXNELElBQUkzRixHQUFHLENBQUNxQyxhQUFhLENBQUN3QixPQUFPLENBQUM7d0RBQUc7Ozs7Ozs7OzJEQUczRTs7Ozs7O2tEQUVOLDhEQUFDSzt3Q0FBSUMsV0FBVTtrREFDWlIsYUFBWWdDLFlBQUFBLElBQUkzRixHQUFHLGNBQVAyRixnQ0FBQUEsVUFBU2hELEdBQUc7Ozs7OztrREFFM0IsOERBQUN1Qjt3Q0FBSUMsV0FBVTtrREFDWlIsYUFBWWdDLFlBQUFBLElBQUkzRixHQUFHLGNBQVAyRixnQ0FBQUEsVUFBU25ELEdBQUc7Ozs7OztrREFFM0IsOERBQUMwQjt3Q0FBSUMsV0FBVyx3Q0FBaUcsT0FBekQyQixZQUFZLDZCQUE2QjtrREFDOUZoQyxjQUFhNkIsWUFBQUEsSUFBSTNGLEdBQUcsY0FBUDJGLGdDQUFBQSxVQUFTckQsTUFBTTs7Ozs7O2tEQUUvQiw4REFBQzRCO3dDQUFJQyxXQUFXLHdDQUFpRyxPQUF6RDJCLFlBQVksNkJBQTZCO2tEQUM5RmhDLGNBQWE2QixZQUFBQSxJQUFJM0YsR0FBRyxjQUFQMkYsZ0NBQUFBLFVBQVNwRCxZQUFZOzs7Ozs7OytCQTdFaENvRCxJQUFJcEcsV0FBVzs7Ozs7d0JBaUYxQjs7Ozs7Ozs7Ozs7O1lBTUwzQyw2QkFDQyw4REFBQ3NIO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ007OENBQUs7Ozs7Ozs4Q0FDTiw4REFBQ0E7OENBQUs7Ozs7Ozs4Q0FDTiw4REFBQ0E7O3dDQUFLO3dDQUFTN0gsWUFBWThDLElBQUksQ0FBQ2IsTUFBTTt3Q0FBQzs7Ozs7Ozs7Ozs7OztzQ0FFekMsOERBQUNxRjs0QkFBSUMsV0FBVTs7Z0NBQXdCO2dDQUN0QixJQUFJNUQsS0FBSzNELFlBQVkwRCxTQUFTLEVBQUV5RixrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8vRTtHQTloQndCeko7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvT3B0aW9uQ2hhaW4udHN4PzVjODEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgT3B0aW9uQ2hhaW5EYXRhLCBPcHRpb25DaGFpblJvdywgT3B0aW9uRGF0YSwgRXhwaXJ5RGF0YSwgTWFya2V0RGF0YSB9IGZyb20gJy4uL3R5cGVzJztcblxuaW50ZXJmYWNlIE9wdGlvbkNoYWluUHJvcHMge1xuICBtYXJrZXREYXRhOiBNYXA8c3RyaW5nLCBNYXJrZXREYXRhPjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gT3B0aW9uQ2hhaW4oeyBtYXJrZXREYXRhIH06IE9wdGlvbkNoYWluUHJvcHMpIHtcbiAgY29uc3QgW2V4cGlyeURhdGEsIHNldEV4cGlyeURhdGFdID0gdXNlU3RhdGU8RXhwaXJ5RGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRFeHBpcnksIHNldFNlbGVjdGVkRXhwaXJ5XSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbb3B0aW9uQ2hhaW4sIHNldE9wdGlvbkNoYWluXSA9IHVzZVN0YXRlPE9wdGlvbkNoYWluRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtuaWZ0eVNwb3RQcmljZSwgc2V0TmlmdHlTcG90UHJpY2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcblxuICAvLyDinIUgRklYRUQ6IEdldCBOSUZUWSBzcG90IHByaWNlIGZyb20gc3Vic2NyaWJlZCBtYXJrZXQgZGF0YSAoc2VjdXJpdHkgSUQgMTMsIGV4Y2hhbmdlIElEWF9JKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIOKchSBGSVhFRDogRGlyZWN0IGxvb2t1cCBmb3IgTklGVFkgc3BvdCAoc2VjdXJpdHkgSUQgMTMpIC0gc2hvdWxkIGJlIHN1YnNjcmliZWQgd2l0aCBJRFhfSSBleGNoYW5nZVxuICAgIGNvbnN0IG5pZnR5RGF0YSA9IG1hcmtldERhdGEuZ2V0KCcxMycpO1xuICAgIGlmIChuaWZ0eURhdGEgJiYgbmlmdHlEYXRhLmx0cCA+IDApIHtcbiAgICAgIHNldE5pZnR5U3BvdFByaWNlKG5pZnR5RGF0YS5sdHApO1xuICAgICAgY29uc29sZS5sb2coJ1tTUE9UXSDwn46vIE5JRlRZIFNwb3QgUHJpY2UgVXBkYXRlZDonLCBuaWZ0eURhdGEubHRwLCAnZnJvbSBzZWN1cml0eSBJRCAxMyAoSURYX0kpJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g4pyFIEVOSEFOQ0VEOiBGYWxsYmFjayBzZWFyY2ggZm9yIGFueSBOSUZUWSBpbmRleCBkYXRhIHdpdGggYmV0dGVyIGxvZ2dpbmdcbiAgICBmb3IgKGNvbnN0IFtzZWN1cml0eUlkLCBkYXRhXSBvZiBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKSkge1xuICAgICAgaWYgKGRhdGEuc3ltYm9sID09PSAnTklGVFknICYmIGRhdGEubHRwID4gMCkge1xuICAgICAgICBzZXROaWZ0eVNwb3RQcmljZShkYXRhLmx0cCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbU1BPVF0g8J+OryBOSUZUWSBTcG90IFByaWNlIEZvdW5kOicsIGRhdGEubHRwLCAnZnJvbSBzZWN1cml0eTonLCBzZWN1cml0eUlkLCAnZXhjaGFuZ2U6JywgZGF0YS5leGNoYW5nZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDinIUgRklYRUQ6IE9ubHkgdXNlIG1vY2sgcHJpY2UgaWYgYWJzb2x1dGVseSBubyByZWFsIGRhdGEgaXMgYXZhaWxhYmxlIGFuZCBsb2cgd2FybmluZ1xuICAgIGlmIChuaWZ0eVNwb3RQcmljZSA9PT0gMCkge1xuICAgICAgY29uc3QgbW9ja1ByaWNlID0gMjQ4NTA7XG4gICAgICBzZXROaWZ0eVNwb3RQcmljZShtb2NrUHJpY2UpO1xuICAgICAgY29uc29sZS53YXJuKCdbU1BPVF0g4pqg77iPIFVzaW5nIG1vY2sgTklGVFkgU3BvdCBQcmljZTonLCBtb2NrUHJpY2UsICcoSU5ERVggZGF0YSBub3QgYXZhaWxhYmxlIC0gY2hlY2sgc3Vic2NyaXB0aW9uKScpO1xuICAgIH1cbiAgfSwgW21hcmtldERhdGFdKTtcblxuICAvLyBGZXRjaCBleHBpcnkgZGF0ZXMgb24gY29tcG9uZW50IG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hFeHBpcnlEYXRlcygpO1xuICB9LCBbXSk7XG5cbiAgLy8gQnVpbGQgb3B0aW9uIGNoYWluIHdoZW4gZXhwaXJ5IGlzIHNlbGVjdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkRXhwaXJ5ICYmIG5pZnR5U3BvdFByaWNlID4gMCkge1xuICAgICAgYnVpbGRPcHRpb25DaGFpbigpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkRXhwaXJ5LCBuaWZ0eVNwb3RQcmljZSwgbWFya2V0RGF0YV0pO1xuXG4gIGNvbnN0IGZldGNoRXhwaXJ5RGF0ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9uaWZ0eS1leHBpcnknKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggZXhwaXJ5IGRhdGVzOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRFeHBpcnlEYXRhKHJlc3VsdC5kYXRhKTtcbiAgICAgICAgLy8gQXV0by1zZWxlY3QgZmlyc3QgZXhwaXJ5XG4gICAgICAgIGlmIChyZXN1bHQuZGF0YS5leHBpcmllcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRFeHBpcnkocmVzdWx0LmRhdGEuZXhwaXJpZXNbMF0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0Lm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBmZXRjaCBleHBpcnkgZGF0ZXMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGV4cGlyeSBkYXRlczonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBidWlsZE9wdGlvbkNoYWluID0gKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRFeHBpcnkgfHwgbmlmdHlTcG90UHJpY2UgPD0gMCkgcmV0dXJuO1xuXG4gICAgY29uc29sZS5sb2coJ/CflJcgQnVpbGRpbmcgb3B0aW9uIGNoYWluIGZvciBleHBpcnk6Jywgc2VsZWN0ZWRFeHBpcnksICdzcG90OicsIG5pZnR5U3BvdFByaWNlKTtcbiAgICBjb25zb2xlLmxvZygn8J+TiiBUb3RhbCBtYXJrZXQgZGF0YSBlbnRyaWVzOicsIG1hcmtldERhdGEuc2l6ZSk7XG5cbiAgICAvLyBEZWJ1ZzogU2hvdyBzYW1wbGUgbWFya2V0IGRhdGEgZW50cmllc1xuICAgIGNvbnN0IHNhbXBsZUVudHJpZXMgPSBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKS5zbGljZSgwLCAxMCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4sgU2FtcGxlIG1hcmtldCBkYXRhIGVudHJpZXM6Jywgc2FtcGxlRW50cmllcy5tYXAoKFtpZCwgZGF0YV0pID0+ICh7XG4gICAgICBzZWN1cml0eUlkOiBpZCxcbiAgICAgIHN5bWJvbDogZGF0YS5zeW1ib2wsXG4gICAgICBleHBpcnlEYXRlOiBkYXRhLmV4cGlyeURhdGUsXG4gICAgICBvcHRpb25UeXBlOiBkYXRhLm9wdGlvblR5cGUsXG4gICAgICBzdHJpa2VQcmljZTogZGF0YS5zdHJpa2VQcmljZVxuICAgIH0pKSk7XG5cbiAgICAvLyBHZW5lcmF0ZSBzdHJpa2UgcHJpY2VzIGFyb3VuZCBzcG90IHByaWNlXG4gICAgY29uc3Qgc3RyaWtlcyA9IGdlbmVyYXRlU3RyaWtlcyhuaWZ0eVNwb3RQcmljZSk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gR2VuZXJhdGVkIHN0cmlrZXM6Jywgc3RyaWtlcyk7XG5cbiAgICAvLyBCdWlsZCBvcHRpb24gY2hhaW4gcm93c1xuICAgIGNvbnN0IHJvd3M6IE9wdGlvbkNoYWluUm93W10gPSBzdHJpa2VzLm1hcChzdHJpa2UgPT4ge1xuICAgICAgY29uc3QgY2FsbERhdGEgPSBmaW5kT3B0aW9uRGF0YShzdHJpa2UsICdDRScpO1xuICAgICAgY29uc3QgcHV0RGF0YSA9IGZpbmRPcHRpb25EYXRhKHN0cmlrZSwgJ1BFJyk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0cmlrZVByaWNlOiBzdHJpa2UsXG4gICAgICAgIGNhbGw6IGNhbGxEYXRhLFxuICAgICAgICBwdXQ6IHB1dERhdGFcbiAgICAgIH07XG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZygn8J+TiCBPcHRpb24gY2hhaW4gcm93cyBidWlsdDonLCByb3dzLmxlbmd0aCwgJ3Jvd3MnKTtcbiAgICBjb25zb2xlLmxvZygn4pyFIEZvdW5kIG9wdGlvbnM6Jywgcm93cy5maWx0ZXIociA9PiByLmNhbGwgfHwgci5wdXQpLmxlbmd0aCwgJ3N0cmlrZXMgd2l0aCBkYXRhJyk7XG5cbiAgICBzZXRPcHRpb25DaGFpbih7XG4gICAgICB1bmRlcmx5aW5nOiAnTklGVFknLFxuICAgICAgc3BvdFByaWNlOiBuaWZ0eVNwb3RQcmljZSxcbiAgICAgIGV4cGlyeTogc2VsZWN0ZWRFeHBpcnksXG4gICAgICByb3dzLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVTdHJpa2VzID0gKHNwb3RQcmljZTogbnVtYmVyKTogbnVtYmVyW10gPT4ge1xuICAgIC8vIEZpcnN0LCBnZXQgYWxsIGF2YWlsYWJsZSBzdHJpa2VzIGZyb20gbWFya2V0IGRhdGEgZm9yIHRoZSBzZWxlY3RlZCBleHBpcnlcbiAgICBjb25zdCBhdmFpbGFibGVTdHJpa2VzID0gbmV3IFNldDxudW1iZXI+KCk7XG5cbiAgICBmb3IgKGNvbnN0IFtzZWN1cml0eUlkLCBkYXRhXSBvZiBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKSkge1xuICAgICAgaWYgKGRhdGEuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWScpICYmXG4gICAgICAgICAgZGF0YS5zeW1ib2wuaW5jbHVkZXMoZm9ybWF0RXhwaXJ5Rm9yU3ltYm9sKHNlbGVjdGVkRXhwaXJ5KSkpIHtcblxuICAgICAgICAvLyBFeHRyYWN0IHN0cmlrZSBmcm9tIHN5bWJvbCAoZS5nLiwgTklGVFktSnVuMjAyNS0yNDg1MC1DRSlcbiAgICAgICAgY29uc3Qgc3ltYm9sUGFydHMgPSBkYXRhLnN5bWJvbC5zcGxpdCgnLScpO1xuICAgICAgICBpZiAoc3ltYm9sUGFydHMubGVuZ3RoID49IDQpIHtcbiAgICAgICAgICBjb25zdCBzdHJpa2UgPSBwYXJzZUZsb2F0KHN5bWJvbFBhcnRzWzJdKTtcbiAgICAgICAgICBpZiAoIWlzTmFOKHN0cmlrZSkpIHtcbiAgICAgICAgICAgIGF2YWlsYWJsZVN0cmlrZXMuYWRkKHN0cmlrZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGF2YWlsYWJsZVN0cmlrZXMuc2l6ZSA9PT0gMCkge1xuICAgICAgLy8gRmFsbGJhY2s6IGdlbmVyYXRlIHN0cmlrZXMgYXJvdW5kIHNwb3QgcHJpY2VcbiAgICAgIGNvbnN0IHN0cmlrZXM6IG51bWJlcltdID0gW107XG4gICAgICBjb25zdCBiYXNlU3RyaWtlID0gTWF0aC5yb3VuZChzcG90UHJpY2UgLyA1MCkgKiA1MDtcbiAgICAgIGZvciAobGV0IGkgPSAtMTA7IGkgPD0gMTA7IGkrKykge1xuICAgICAgICBzdHJpa2VzLnB1c2goYmFzZVN0cmlrZSArIChpICogNTApKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzdHJpa2VzLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcbiAgICB9XG5cbiAgICAvLyBDb252ZXJ0IHRvIHNvcnRlZCBhcnJheVxuICAgIGNvbnN0IGFsbFN0cmlrZXMgPSBBcnJheS5mcm9tKGF2YWlsYWJsZVN0cmlrZXMpLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcblxuICAgIC8vIEZpbmQgQVRNIHN0cmlrZSAoY2xvc2VzdCB0byBzcG90IHByaWNlKVxuICAgIGNvbnN0IGF0bVN0cmlrZSA9IGFsbFN0cmlrZXMucmVkdWNlKChwcmV2LCBjdXJyKSA9PlxuICAgICAgTWF0aC5hYnMoY3VyciAtIHNwb3RQcmljZSkgPCBNYXRoLmFicyhwcmV2IC0gc3BvdFByaWNlKSA/IGN1cnIgOiBwcmV2XG4gICAgKTtcblxuICAgIGNvbnNvbGUubG9nKGBbU1RSSUtFXSDwn46vIEFUTSBTdHJpa2UgaWRlbnRpZmllZDogJHthdG1TdHJpa2V9IChTcG90OiAke3Nwb3RQcmljZX0pYCk7XG4gICAgY29uc29sZS5sb2coYFtTVFJJS0VdIPCfk4ogQXZhaWxhYmxlIHN0cmlrZXM6ICR7YWxsU3RyaWtlcy5sZW5ndGh9YCk7XG5cbiAgICAvLyDinIUgRU5IQU5DRUQ6IFNlbGVjdCBtb3JlIHN0cmlrZXMgYXJvdW5kIEFUTSAowrExMiBzdHJpa2VzIGZvciBiZXR0ZXIgY292ZXJhZ2UpXG4gICAgY29uc3QgYXRtSW5kZXggPSBhbGxTdHJpa2VzLmluZGV4T2YoYXRtU3RyaWtlKTtcbiAgICBjb25zdCBzZWxlY3RlZFN0cmlrZXMgPSBbXG4gICAgICAuLi5hbGxTdHJpa2VzLnNsaWNlKE1hdGgubWF4KDAsIGF0bUluZGV4IC0gMTIpLCBhdG1JbmRleCksXG4gICAgICBhdG1TdHJpa2UsXG4gICAgICAuLi5hbGxTdHJpa2VzLnNsaWNlKGF0bUluZGV4ICsgMSwgYXRtSW5kZXggKyAxMylcbiAgICBdO1xuXG4gICAgY29uc29sZS5sb2coYFtTVFJJS0VdIOKchSBTZWxlY3RlZCAke3NlbGVjdGVkU3RyaWtlcy5sZW5ndGh9IHN0cmlrZXMgYXJvdW5kIEFUTTpgLCBzZWxlY3RlZFN0cmlrZXMpO1xuXG4gICAgcmV0dXJuIHNlbGVjdGVkU3RyaWtlcztcbiAgfTtcblxuICBjb25zdCBmaW5kT3B0aW9uRGF0YSA9IChzdHJpa2U6IG51bWJlciwgb3B0aW9uVHlwZTogJ0NFJyB8ICdQRScpOiBPcHRpb25EYXRhIHwgbnVsbCA9PiB7XG4gICAgY29uc29sZS5sb2coYFtTRUFSQ0hdIExvb2tpbmcgZm9yICR7b3B0aW9uVHlwZX0gJHtzdHJpa2V9IHdpdGggZXhwaXJ5ICR7c2VsZWN0ZWRFeHBpcnl9YCk7XG5cbiAgICAvLyDinIUgRklYRUQ6IE1hdGNoIGJ5IGV4YWN0IGV4cGlyeSBkYXRlIGZyb20gQ1NWIFNNX0VYUElSWV9EQVRFIGNvbHVtblxuICAgIGZvciAoY29uc3QgW3NlY3VyaXR5SWQsIGRhdGFdIG9mIEFycmF5LmZyb20obWFya2V0RGF0YS5lbnRyaWVzKCkpKSB7XG4gICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgTklGVFkgb3B0aW9uIHdpdGggbWF0Y2hpbmcgY3JpdGVyaWFcbiAgICAgIGlmIChkYXRhLnN5bWJvbC5pbmNsdWRlcygnTklGVFktJykgJiYgIC8vIEV4YWN0IE5JRlRZIG9wdGlvbnMgKG5vdCBCQU5LTklGVFksIGV0Yy4pXG4gICAgICAgICAgZGF0YS5zeW1ib2wuaW5jbHVkZXMoYC0ke3N0cmlrZX0tJHtvcHRpb25UeXBlfWApICYmXG4gICAgICAgICAgZGF0YS5leHBpcnlEYXRlID09PSBzZWxlY3RlZEV4cGlyeSkgeyAvLyDinIUgRGlyZWN0IGV4cGlyeSBkYXRlIG1hdGNoIGZyb20gQ1NWIFNNX0VYUElSWV9EQVRFXG5cbiAgICAgICAgY29uc29sZS5sb2coYFtPUFRJT05dIOKchSBGb3VuZCAke29wdGlvblR5cGV9ICR7c3RyaWtlfTogJHtkYXRhLnN5bWJvbH0gKEV4cGlyeTogJHtkYXRhLmV4cGlyeURhdGV9KWApO1xuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc2VjdXJpdHlJZCxcbiAgICAgICAgICBzeW1ib2w6IGRhdGEuc3ltYm9sLFxuICAgICAgICAgIGV4Y2hhbmdlOiBkYXRhLmV4Y2hhbmdlLFxuICAgICAgICAgIHN0cmlrZVByaWNlOiBzdHJpa2UsXG4gICAgICAgICAgb3B0aW9uVHlwZSxcbiAgICAgICAgICBleHBpcnlEYXRlOiBzZWxlY3RlZEV4cGlyeSxcbiAgICAgICAgICBsdHA6IGRhdGEubHRwIHx8IDAsXG4gICAgICAgICAgY2hhbmdlOiBkYXRhLmNoYW5nZSB8fCAwLFxuICAgICAgICAgIGNoYW5nZVBlcmNlbnQ6IGRhdGEuY2hhbmdlUGVyY2VudCB8fCAwLFxuICAgICAgICAgIHZvbHVtZTogZGF0YS52b2x1bWUgfHwgMCxcbiAgICAgICAgICBvcGVuSW50ZXJlc3Q6IGRhdGEub3BlbkludGVyZXN0LFxuICAgICAgICAgIGJpZDogZGF0YS5tYXJrZXREZXB0aD8uWzBdPy5iaWRQcmljZSB8fCBkYXRhLmJpZCxcbiAgICAgICAgICBhc2s6IGRhdGEubWFya2V0RGVwdGg/LlswXT8uYXNrUHJpY2UgfHwgZGF0YS5hc2ssXG4gICAgICAgICAgYmlkUXR5OiBkYXRhLm1hcmtldERlcHRoPy5bMF0/LmJpZFF0eSB8fCBkYXRhLmJpZFF0eSxcbiAgICAgICAgICBhc2tRdHk6IGRhdGEubWFya2V0RGVwdGg/LlswXT8uYXNrUXR5IHx8IGRhdGEuYXNrUXR5LFxuICAgICAgICAgIGhpZ2g6IGRhdGEuaGlnaCxcbiAgICAgICAgICBsb3c6IGRhdGEubG93LFxuICAgICAgICAgIG9wZW46IGRhdGEub3BlbixcbiAgICAgICAgICBjbG9zZTogZGF0YS5jbG9zZSxcbiAgICAgICAgICB0aW1lc3RhbXA6IGRhdGEudGltZXN0YW1wXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRGVidWc6IExvZyB3aGVuIG9wdGlvbiBub3QgZm91bmRcbiAgICBjb25zb2xlLmxvZyhgW09QVElPTl0g4p2MIE5vdCBmb3VuZCAke29wdGlvblR5cGV9ICR7c3RyaWtlfSBmb3IgZXhwaXJ5ICR7c2VsZWN0ZWRFeHBpcnl9YCk7XG5cbiAgICAvLyBTaG93IGF2YWlsYWJsZSBOSUZUWSBvcHRpb25zIGZvciBkZWJ1Z2dpbmdcbiAgICBjb25zdCBuaWZ0eU9wdGlvbnMgPSBBcnJheS5mcm9tKG1hcmtldERhdGEudmFsdWVzKCkpXG4gICAgICAuZmlsdGVyKGRhdGEgPT4gZGF0YS5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZLScpKVxuICAgICAgLnNsaWNlKDAsIDEwKTtcblxuICAgIGNvbnNvbGUubG9nKGBbREVCVUddIEF2YWlsYWJsZSBOSUZUWSBvcHRpb25zICgke25pZnR5T3B0aW9ucy5sZW5ndGh9IHRvdGFsKTpgLCBuaWZ0eU9wdGlvbnMubWFwKG9wdCA9PlxuICAgICAgYCR7b3B0LnN5bWJvbH0gKEV4cGlyeTogJHtvcHQuZXhwaXJ5RGF0ZX0sIFN0cmlrZTogJHtvcHQuc3RyaWtlUHJpY2V9LCBUeXBlOiAke29wdC5vcHRpb25UeXBlfSlgXG4gICAgKSk7XG5cbiAgICAvLyBTaG93IHNwZWNpZmljIHR5cGUgb3B0aW9uc1xuICAgIGNvbnN0IHR5cGVPcHRpb25zID0gQXJyYXkuZnJvbShtYXJrZXREYXRhLnZhbHVlcygpKVxuICAgICAgLmZpbHRlcihkYXRhID0+IGRhdGEuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWS0nKSAmJiBkYXRhLnN5bWJvbC5pbmNsdWRlcyhgLSR7b3B0aW9uVHlwZX1gKSlcbiAgICAgIC5zbGljZSgwLCA1KTtcblxuICAgIGNvbnNvbGUubG9nKGBbREVCVUddIEF2YWlsYWJsZSAke29wdGlvblR5cGV9IG9wdGlvbnM6YCwgdHlwZU9wdGlvbnMubWFwKG9wdCA9PlxuICAgICAgYCR7b3B0LnN5bWJvbH0gKEV4cGlyeTogJHtvcHQuZXhwaXJ5RGF0ZX0pYFxuICAgICkpO1xuXG4gICAgcmV0dXJuIG51bGw7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0RXhwaXJ5Rm9yU3ltYm9sID0gKGV4cGlyeTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICAvLyBDb252ZXJ0IFlZWVktTU0tREQgdG8gZm9ybWF0IHVzZWQgaW4gc3ltYm9scyAoZS5nLiwgSnVuMjAyNSlcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZXhwaXJ5KTtcbiAgICBjb25zdCBtb250aHMgPSBbJ0phbicsICdGZWInLCAnTWFyJywgJ0FwcicsICdNYXknLCAnSnVuJyxcbiAgICAgICAgICAgICAgICAgICAnSnVsJywgJ0F1ZycsICdTZXAnLCAnT2N0JywgJ05vdicsICdEZWMnXTtcbiAgICByZXR1cm4gYCR7bW9udGhzW2RhdGUuZ2V0TW9udGgoKV19JHtkYXRlLmdldEZ1bGxZZWFyKCl9YDtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIXByaWNlIHx8IHByaWNlIDw9IDApIHJldHVybiAnTi9BJztcbiAgICByZXR1cm4gYOKCuSR7cHJpY2UudG9GaXhlZCgyKX1gO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdE51bWJlciA9IChudW06IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFudW0pIHJldHVybiAnTi9BJztcbiAgICByZXR1cm4gbnVtLnRvTG9jYWxlU3RyaW5nKCk7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2hhbmdlQ29sb3IgPSAoY2hhbmdlOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmICghY2hhbmdlKSByZXR1cm4gJ3RleHQtZ3JheS00MDAnO1xuICAgIHJldHVybiBjaGFuZ2UgPiAwID8gJ3RleHQtZ3JlZW4tNDAwJyA6IGNoYW5nZSA8IDAgPyAndGV4dC1yZWQtNDAwJyA6ICd0ZXh0LWdyYXktNDAwJztcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgb3B0aW9uIGNoYWluLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDAgZm9udC1tZWRpdW1cIj5FcnJvciBMb2FkaW5nIE9wdGlvbiBDaGFpbjwvaDM+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCB0ZXh0LXNtIG10LTFcIj57ZXJyb3J9PC9wPlxuICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoRXhwaXJ5RGF0ZXN9XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiBweC0zIHB5LTEgYmctcmVkLTYwMCB0ZXh0LXdoaXRlIHRleHQtc20gcm91bmRlZCBob3ZlcjpiZy1yZWQtNzAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIFJldHJ5XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBtaW4taC1zY3JlZW5cIj5cbiAgICAgIHsvKiBIZWFkZXIgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC02XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5PcHRpb25zPC9oMT5cblxuICAgICAgICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgYmctYmx1ZS01MCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgICAg8J+UjSBOSUZUWVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgIFN0cmF0ZWd5IGJ1aWxkZXJcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICBDbGFzc1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgIFZvbGF0aWxpdHlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBSaWdodCBzaWRlIGNvbnRyb2xzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICBCeSBleHBpcmF0aW9uIHwgYnkgc3RyaWtlXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk5JRlRZIFNwb3Q6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKG5pZnR5U3BvdFByaWNlKX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICB7bmlmdHlTcG90UHJpY2UgPT09IDI0ODUwICYmIChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj4oTW9jayk8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEV4cGlyeSBTZWxlY3Rpb24gLSBFbmhhbmNlZCBGb3JtYXQgKi99XG4gICAgICB7ZXhwaXJ5RGF0YSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG92ZXJmbG93LXgtYXV0byBwYi0yXCI+XG4gICAgICAgICAgICB7ZXhwaXJ5RGF0YS5leHBpcmllcy5zbGljZSgwLCAxNSkubWFwKChleHBpcnksIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShleHBpcnkpO1xuICAgICAgICAgICAgICBjb25zdCBpc1NlbGVjdGVkID0gZXhwaXJ5ID09PSBzZWxlY3RlZEV4cGlyeTtcbiAgICAgICAgICAgICAgY29uc3QgaXNUb2RheSA9IGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCk7XG4gICAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudFdlZWsgPSBNYXRoLmFicyhkYXRlLmdldFRpbWUoKSAtIG5ldyBEYXRlKCkuZ2V0VGltZSgpKSA8IDcgKiAyNCAqIDYwICogNjAgKiAxMDAwO1xuXG4gICAgICAgICAgICAgIC8vIEVuaGFuY2VkIGRhdGUgZm9ybWF0dGluZ1xuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcbiAgICAgICAgICAgICAgY29uc3QgZXhwaXJ5WWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTtcbiAgICAgICAgICAgICAgY29uc3QgbW9udGhTaG9ydCA9IGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdzaG9ydCcgfSk7XG4gICAgICAgICAgICAgIGNvbnN0IGRheSA9IGRhdGUuZ2V0RGF0ZSgpO1xuXG4gICAgICAgICAgICAgIC8vIFNob3cgeWVhciBpZiBkaWZmZXJlbnQgZnJvbSBjdXJyZW50IHllYXJcbiAgICAgICAgICAgICAgY29uc3Qgc2hvd1llYXIgPSBleHBpcnlZZWFyICE9PSBjdXJyZW50WWVhcjtcblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17ZXhwaXJ5fVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRFeHBpcnkoZXhwaXJ5KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgcHgtMyBweS0yIHRleHQteHMgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgYm9yZGVyICR7XG4gICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibGFjayB0ZXh0LXdoaXRlIGJvcmRlci1ibGFjaydcbiAgICAgICAgICAgICAgICAgICAgICA6IGlzVG9kYXlcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1vcmFuZ2UtNTAwIHRleHQtd2hpdGUgYm9yZGVyLW9yYW5nZS01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiBpc0N1cnJlbnRXZWVrXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCBib3JkZXItZ3JheS0zMDAgaG92ZXI6YmctZ3JheS0yMDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctd2hpdGUgdGV4dC1ncmF5LTcwMCBib3JkZXItZ3JheS0zMDAgaG92ZXI6YmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWluLXctWzQwcHhdXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW5vcm1hbCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21vbnRoU2hvcnR9e3Nob3dZZWFyICYmIGAgJHtleHBpcnlZZWFyLnRvU3RyaW5nKCkuc2xpY2UoLTIpfWB9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2RheX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEVuaGFuY2VkIERlYnVnIEluZm8gKi99XG4gICAgICAgICAge3Byb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyB0ZXh0LXhzIHRleHQtZ3JheS01MDAgYmctZ3JheS0xMDAgcC0zIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPlNlbGVjdGVkIEV4cGlyeTo8L3N0cm9uZz4ge3NlbGVjdGVkRXhwaXJ5fTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5OSUZUWSBTcG90Ojwvc3Ryb25nPiDigrl7bmlmdHlTcG90UHJpY2V9IHtuaWZ0eVNwb3RQcmljZSA9PT0gMjQ4NTAgPyAnKE1vY2spJyA6ICcoTGl2ZSknfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5BdmFpbGFibGUgU3RyaWtlczo8L3N0cm9uZz4ge29wdGlvbkNoYWluPy5yb3dzLmxlbmd0aCB8fCAwfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5NYXJrZXQgRGF0YSBTaXplOjwvc3Ryb25nPiB7bWFya2V0RGF0YS5zaXplfSBpbnN0cnVtZW50czwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5OSUZUWSBPcHRpb25zOjwvc3Ryb25nPiB7QXJyYXkuZnJvbShtYXJrZXREYXRhLnZhbHVlcygpKS5maWx0ZXIoZCA9PiBkLnN5bWJvbC5pbmNsdWRlcygnTklGVFktJykpLmxlbmd0aH0gZm91bmQ8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+T3B0aW9ucyB3aXRoIEV4cGlyeTo8L3N0cm9uZz4ge0FycmF5LmZyb20obWFya2V0RGF0YS52YWx1ZXMoKSkuZmlsdGVyKGQgPT4gZC5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZLScpICYmIGQuZXhwaXJ5RGF0ZSkubGVuZ3RofSBtYXRjaGVkPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogT3B0aW9uIENoYWluIFRhYmxlICovfVxuICAgICAge29wdGlvbkNoYWluICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZVwiPlxuICAgICAgICAgIHsvKiBUYWJsZSBIZWFkZXJzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBiZy1ncmF5LTUwIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICB7LyogQ2FsbHMgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBib3JkZXItciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgZm9udC1ib2xkXCI+Q2FsbHM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBTdHJpa2UgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIHRleHQtY2VudGVyIHB5LTMgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtYm9sZFwiPlN0cmlrZTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFB1dHMgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgZm9udC1ib2xkXCI+UHV0czwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIENvbHVtbiBIZWFkZXJzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBiZy1ncmF5LTEwMCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgey8qIENhbGwgY29sdW1ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPk9JPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5Wb2x1bWU8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkJpZDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+QXNrPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5DaGFuZ2U8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkxUUDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMFwiPlNlY3VyaXR5IElEPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTdHJpa2UgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgdGV4dC1jZW50ZXIgcHktMiBweC0xIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmb250LWJvbGRcIj5TdHJpa2U8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFB1dCBjb2x1bW5zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+U2VjdXJpdHkgSUQ8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkxUUDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+Q2hhbmdlPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5Bc2s8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkJpZDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+Vm9sdW1lPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5PSTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRhYmxlIEJvZHkgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkaXZpZGUteSBkaXZpZGUtZ3JheS0xMDBcIj5cbiAgICAgICAgICAgIHtvcHRpb25DaGFpbi5yb3dzLm1hcCgocm93LCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBpc0FUTSA9IE1hdGguYWJzKHJvdy5zdHJpa2VQcmljZSAtIG5pZnR5U3BvdFByaWNlKSA8PSA1MDtcbiAgICAgICAgICAgICAgY29uc3QgaXNJVE1fQ2FsbCA9IHJvdy5zdHJpa2VQcmljZSA8IG5pZnR5U3BvdFByaWNlO1xuICAgICAgICAgICAgICBjb25zdCBpc0lUTV9QdXQgPSByb3cuc3RyaWtlUHJpY2UgPiBuaWZ0eVNwb3RQcmljZTtcblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17cm93LnN0cmlrZVByaWNlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGlzQVRNID8gJ2JnLXllbGxvdy01MCcgOiBpbmRleCAlIDIgPT09IDAgPyAnYmctd2hpdGUnIDogJ2JnLWdyYXktMjUnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7LyogQ2FsbCBPcHRpb25zIERhdGEgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSAke2lzSVRNX0NhbGwgPyAndGV4dC1ncmVlbi03MDAgZm9udC1tZWRpdW0nIDogJ3RleHQtZ3JheS03MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5jYWxsPy5vcGVuSW50ZXJlc3QpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSAke2lzSVRNX0NhbGwgPyAndGV4dC1ncmVlbi03MDAgZm9udC1tZWRpdW0nIDogJ3RleHQtZ3JheS03MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5jYWxsPy52b2x1bWUpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LmNhbGw/LmJpZCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LmNhbGw/LmFzayl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3Iocm93LmNhbGw/LmNoYW5nZSl9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtyb3cuY2FsbD8uY2hhbmdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cm93LmNhbGwuY2hhbmdlID4gMCA/ICcrJyA6ICcnfXtyb3cuY2FsbC5jaGFuZ2UudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAoe3Jvdy5jYWxsLmNoYW5nZVBlcmNlbnQgPiAwID8gJysnIDogJyd9e3Jvdy5jYWxsLmNoYW5nZVBlcmNlbnQudG9GaXhlZCgxKX0lKVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gZm9udC1ib2xkICR7XG4gICAgICAgICAgICAgICAgICAgIGlzSVRNX0NhbGwgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cuY2FsbD8ubHRwKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQteHMgZm9udC1tb25vIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCAke1xuICAgICAgICAgICAgICAgICAgICBpc0lUTV9DYWxsID8gJ3RleHQtZ3JlZW4tNjAwJyA6ICd0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7cm93LmNhbGw/LnNlY3VyaXR5SWQgfHwgJ04vQSd9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFN0cmlrZSBQcmljZSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0yMCB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LWJvbGQgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwICR7XG4gICAgICAgICAgICAgICAgICAgIGlzQVRNID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyA6ICd0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7cm93LnN0cmlrZVByaWNlfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBQdXQgT3B0aW9ucyBEYXRhICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQteHMgZm9udC1tb25vICR7XG4gICAgICAgICAgICAgICAgICAgIGlzSVRNX1B1dCA/ICd0ZXh0LXJlZC02MDAnIDogJ3RleHQtZ3JheS01MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtyb3cucHV0Py5zZWN1cml0eUlkIHx8ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LWJvbGQgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNJVE1fUHV0ID8gJ3RleHQtcmVkLTYwMCcgOiAndGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHJvdy5wdXQ/Lmx0cCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3Iocm93LnB1dD8uY2hhbmdlKX1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Jvdy5wdXQ/LmNoYW5nZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Jvdy5wdXQuY2hhbmdlID4gMCA/ICcrJyA6ICcnfXtyb3cucHV0LmNoYW5nZS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICh7cm93LnB1dC5jaGFuZ2VQZXJjZW50ID4gMCA/ICcrJyA6ICcnfXtyb3cucHV0LmNoYW5nZVBlcmNlbnQudG9GaXhlZCgxKX0lKVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cucHV0Py5hc2spfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LnB1dD8uYmlkKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gJHtpc0lUTV9QdXQgPyAndGV4dC1yZWQtNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cucHV0Py52b2x1bWUpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSAke2lzSVRNX1B1dCA/ICd0ZXh0LXJlZC03MDAgZm9udC1tZWRpdW0nIDogJ3RleHQtZ3JheS03MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5wdXQ/Lm9wZW5JbnRlcmVzdCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICB7b3B0aW9uQ2hhaW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4+U2hvdyBhbGw8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPuKAojwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+U2hvd2luZyB7b3B0aW9uQ2hhaW4ucm93cy5sZW5ndGh9IHN0cmlrZXMgYXJvdW5kIEFUTTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgTGFzdCB1cGRhdGVkOiB7bmV3IERhdGUob3B0aW9uQ2hhaW4udGltZXN0YW1wKS50b0xvY2FsZVRpbWVTdHJpbmcoKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJPcHRpb25DaGFpbiIsIm1hcmtldERhdGEiLCJleHBpcnlEYXRhIiwic2V0RXhwaXJ5RGF0YSIsInNlbGVjdGVkRXhwaXJ5Iiwic2V0U2VsZWN0ZWRFeHBpcnkiLCJvcHRpb25DaGFpbiIsInNldE9wdGlvbkNoYWluIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibmlmdHlTcG90UHJpY2UiLCJzZXROaWZ0eVNwb3RQcmljZSIsIm5pZnR5RGF0YSIsImdldCIsImx0cCIsImNvbnNvbGUiLCJsb2ciLCJzZWN1cml0eUlkIiwiZGF0YSIsIkFycmF5IiwiZnJvbSIsImVudHJpZXMiLCJzeW1ib2wiLCJleGNoYW5nZSIsIm1vY2tQcmljZSIsIndhcm4iLCJmZXRjaEV4cGlyeURhdGVzIiwiYnVpbGRPcHRpb25DaGFpbiIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwic3RhdHVzVGV4dCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiZXhwaXJpZXMiLCJsZW5ndGgiLCJtZXNzYWdlIiwiZXJyIiwic2l6ZSIsInNhbXBsZUVudHJpZXMiLCJzbGljZSIsIm1hcCIsImlkIiwiZXhwaXJ5RGF0ZSIsIm9wdGlvblR5cGUiLCJzdHJpa2VQcmljZSIsInN0cmlrZXMiLCJnZW5lcmF0ZVN0cmlrZXMiLCJyb3dzIiwic3RyaWtlIiwiY2FsbERhdGEiLCJmaW5kT3B0aW9uRGF0YSIsInB1dERhdGEiLCJjYWxsIiwicHV0IiwiZmlsdGVyIiwiciIsInVuZGVybHlpbmciLCJzcG90UHJpY2UiLCJleHBpcnkiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwiYXZhaWxhYmxlU3RyaWtlcyIsIlNldCIsImluY2x1ZGVzIiwiZm9ybWF0RXhwaXJ5Rm9yU3ltYm9sIiwic3ltYm9sUGFydHMiLCJzcGxpdCIsInBhcnNlRmxvYXQiLCJpc05hTiIsImFkZCIsImJhc2VTdHJpa2UiLCJNYXRoIiwicm91bmQiLCJpIiwicHVzaCIsInNvcnQiLCJhIiwiYiIsImFsbFN0cmlrZXMiLCJhdG1TdHJpa2UiLCJyZWR1Y2UiLCJwcmV2IiwiY3VyciIsImFicyIsImF0bUluZGV4IiwiaW5kZXhPZiIsInNlbGVjdGVkU3RyaWtlcyIsIm1heCIsImNoYW5nZSIsImNoYW5nZVBlcmNlbnQiLCJ2b2x1bWUiLCJvcGVuSW50ZXJlc3QiLCJiaWQiLCJtYXJrZXREZXB0aCIsImJpZFByaWNlIiwiYXNrIiwiYXNrUHJpY2UiLCJiaWRRdHkiLCJhc2tRdHkiLCJoaWdoIiwibG93Iiwib3BlbiIsImNsb3NlIiwibmlmdHlPcHRpb25zIiwidmFsdWVzIiwib3B0IiwidHlwZU9wdGlvbnMiLCJkYXRlIiwibW9udGhzIiwiZ2V0TW9udGgiLCJnZXRGdWxsWWVhciIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJ0b0ZpeGVkIiwiZm9ybWF0TnVtYmVyIiwibnVtIiwidG9Mb2NhbGVTdHJpbmciLCJnZXRDaGFuZ2VDb2xvciIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMSIsInNwYW4iLCJpbmRleCIsImlzU2VsZWN0ZWQiLCJpc1RvZGF5IiwidG9EYXRlU3RyaW5nIiwiaXNDdXJyZW50V2VlayIsImdldFRpbWUiLCJjdXJyZW50WWVhciIsImV4cGlyeVllYXIiLCJtb250aFNob3J0IiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwibW9udGgiLCJkYXkiLCJnZXREYXRlIiwic2hvd1llYXIiLCJ0b1N0cmluZyIsInN0cm9uZyIsImQiLCJyb3ciLCJpc0FUTSIsImlzSVRNX0NhbGwiLCJpc0lUTV9QdXQiLCJ0b0xvY2FsZVRpbWVTdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});