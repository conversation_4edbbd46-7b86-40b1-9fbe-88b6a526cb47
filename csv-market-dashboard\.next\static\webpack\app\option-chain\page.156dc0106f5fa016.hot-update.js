"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDCC5 Fetched expiry data:\", result);\n            if (result.success) {\n                setExpiryData(result.data);\n                console.log(\"\\uD83D\\uDCCB Available expiry dates:\", result.data.expiries);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    const firstExpiry = result.data.expiries[0];\n                    console.log(\"\\uD83C\\uDFAF Auto-selecting first expiry:\", firstExpiry);\n                    setSelectedExpiry(firstExpiry);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        console.log(\"\\uD83D\\uDCCA Total market data entries:\", marketData.size);\n        // Debug: Show sample market data entries\n        const sampleEntries = Array.from(marketData.entries()).slice(0, 10);\n        console.log(\"\\uD83D\\uDCCB Sample market data entries:\", sampleEntries.map((param)=>{\n            let [id, data] = param;\n            return {\n                securityId: id,\n                symbol: data.symbol,\n                expiryDate: data.expiryDate,\n                optionType: data.optionType,\n                strikePrice: data.strikePrice\n            };\n        }));\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        console.log(\"\\uD83C\\uDFAF Generated strikes:\", strikes);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        console.log(\"\\uD83D\\uDCC8 Option chain rows built:\", rows.length, \"rows\");\n        console.log(\"✅ Found options:\", rows.filter((r)=>r.call || r.put).length, \"strikes with data\");\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        console.log(\"[SEARCH] Looking for \".concat(optionType, \" \").concat(strike, \" with expiry \").concat(selectedExpiry));\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n        // Show available NIFTY options for debugging\n        const niftyOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\")).slice(0, 10);\n        console.log(\"[DEBUG] Available NIFTY options (\".concat(niftyOptions.length, \" total):\"), niftyOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \", Strike: \").concat(opt.strikePrice, \", Type: \").concat(opt.optionType, \")\")));\n        // Show specific type options\n        const typeOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n        console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), typeOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_call7, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6, _row_put7;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-500\"),\n                                        children: ((_row_call7 = row.call) === null || _row_call7 === void 0 ? void 0 : _row_call7.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-500\"),\n                                        children: ((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change)),\n                                        children: ((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put7 = row.put) === null || _row_put7 === void 0 ? void 0 : _row_put7.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 405,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"3MYzdkz2i0N1g/MoBahr6e4MTpU=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});