// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Instruments {
  EXCH_ID                 String
  SEGMENT                 String
  SECURITY_ID             String  @id
  ISIN                    String?
  INSTRUMENT              String
  UNDERLYING_SECURITY_ID  String?
  UNDERLYING_SYMBOL       String?
  SYMBOL_NAME             String
  DISPLAY_NAME            String?
  INSTRUMENT_TYPE         String?
  STRIKE_PRICE            String?
  OPTION_TYPE             String?
  SM_EXPIRY_DATE          String?
  LOT_SIZE                String?
  TICK_SIZE               String?
  MULTIPLIER              String?
  FREEZE_QTY              String?
  WARNING_QTY             String?
  DAILY_PRICE_BAND_LOWER  String?
  DAILY_PRICE_BAND_UPPER  String?
  TRADE_PRICE_BAND_LOWER  String?
  TRADE_PRICE_BAND_UPPER  String?
  MTF_LEVERAGE            String?

  @@map("Instruments")
}


