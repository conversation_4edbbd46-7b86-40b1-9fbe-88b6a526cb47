"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n            console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), availableOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_call7, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-xs font-mono border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-500\"),\n                                        children: ((_row_call7 = row.call) === null || _row_call7 === void 0 ? void 0 : _row_call7.securityId) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.change)),\n                                        children: ((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"3MYzdkz2i0N1g/MoBahr6e4MTpU=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});