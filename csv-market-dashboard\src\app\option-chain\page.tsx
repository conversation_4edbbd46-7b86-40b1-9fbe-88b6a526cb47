'use client';

import { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import Option<PERSON>hain from '../../components/OptionChain';
import { MarketData } from '../../types';

export default function OptionChainPage() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(new Map());

  useEffect(() => {
    const newSocket = io('http://localhost:8080', {
      transports: ['websocket'], // Use WebSocket only, no polling
      upgrade: false,
      rememberUpgrade: false
    });
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server for option chain');
      setConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    newSocket.on('marketData', (data: MarketData) => {
      setMarketData(prev => {
        const newMap = new Map(prev);
        newMap.set(data.securityId, data);
        return newMap;
      });
    });

    // Handle subscription disabled messages
    newSocket.on('subscription_disabled', (data: { message: string }) => {
      console.log('Subscription disabled:', data.message);
    });

    return () => {
      newSocket.close();
    };
  }, []);

  // Get all subscribed data (server auto-subscribed)
  const subscribedData = Array.from(marketData.values());

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <a
              href="/"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors"
            >
              ← Main Dashboard
            </a>
            <a
              href="/subscribed"
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors"
            >
              📊 Subscribed Data
            </a>
          </div>

          {/* Status indicators */}
          <div className="flex items-center space-x-4">
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-1 ${connected ? 'bg-green-400' : 'bg-red-400'}`}></div>
              {connected ? 'Connected' : 'Disconnected'}
            </div>
            <span className="text-sm text-gray-600">
              {subscribedData.length} instruments
            </span>
            <span className="text-sm text-gray-600">
              {subscribedData.filter(d => d.ltp > 0).length} active
            </span>
          </div>
        </div>
      </div>

      {/* Option Chain Component */}
      <OptionChain marketData={marketData} />
    </div>
  );
}
