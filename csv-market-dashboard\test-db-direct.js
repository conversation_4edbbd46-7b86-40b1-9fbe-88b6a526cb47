// Direct database connection test
const { Client } = require('pg');

async function testDatabase() {
  // Hardcode the DATABASE_URL to test connection
  const DATABASE_URL = '***************************************************************/postgres';
  
  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('🔍 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database successfully!');

    // Check if Instruments table exists
    console.log('\n📋 Checking for Instruments table...');
    const tableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name ILIKE '%instrument%'
    `);
    
    console.log('Tables found:', tableCheck.rows.map(row => row.table_name));

    if (tableCheck.rows.length === 0) {
      console.log('❌ No Instruments table found. Checking all tables:');
      const allTables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      console.log('All tables:', allTables.rows.map(row => row.table_name));
      return;
    }

    const tableName = tableCheck.rows[0].table_name;
    console.log(`✅ Found table: ${tableName}`);

    // Get table structure
    console.log(`\n🏗️ Getting structure of table '${tableName}'...`);
    const structure = await client.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);

    console.log('\n📊 Table Structure:');
    structure.rows.forEach((col, index) => {
      console.log(`${index + 1}. ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Get row count
    console.log(`\n📈 Getting row count...`);
    const countResult = await client.query(`SELECT COUNT(*) as total FROM "${tableName}"`);
    const totalRows = countResult.rows[0].total;
    console.log(`Total rows: ${totalRows}`);

    // Check for NIFTY options
    console.log(`\n🎯 Checking for NIFTY options...`);
    const niftyCheck = await client.query(`
      SELECT COUNT(*) as count 
      FROM "${tableName}" 
      WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
      AND "OPTION_TYPE" IN ('CE', 'PE')
    `);
    
    console.log(`NIFTY options found: ${niftyCheck.rows[0].count}`);

    if (niftyCheck.rows[0].count > 0) {
      // Get sample NIFTY options
      console.log(`\n📊 Sample NIFTY options:`);
      const niftySample = await client.query(`
        SELECT 
          "SECURITY_ID",
          "SYMBOL_NAME", 
          "SM_EXPIRY_DATE",
          "STRIKE_PRICE",
          "OPTION_TYPE"
        FROM "${tableName}" 
        WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
        AND "OPTION_TYPE" IN ('CE', 'PE')
        ORDER BY "SM_EXPIRY_DATE", "STRIKE_PRICE"::numeric
        LIMIT 10
      `);

      niftySample.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.SYMBOL_NAME} (ID: ${row.SECURITY_ID})`);
        console.log(`   Expiry: ${row.SM_EXPIRY_DATE}, Strike: ${row.STRIKE_PRICE}, Type: ${row.OPTION_TYPE}`);
      });

      // Get available expiry dates
      console.log(`\n📅 Available NIFTY expiry dates:`);
      const expiries = await client.query(`
        SELECT DISTINCT "SM_EXPIRY_DATE"
        FROM "${tableName}" 
        WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
        AND "OPTION_TYPE" IN ('CE', 'PE')
        AND "SM_EXPIRY_DATE" IS NOT NULL
        ORDER BY "SM_EXPIRY_DATE"
        LIMIT 10
      `);

      expiries.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.SM_EXPIRY_DATE}`);
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Database connection closed');
  }
}

testDatabase();
