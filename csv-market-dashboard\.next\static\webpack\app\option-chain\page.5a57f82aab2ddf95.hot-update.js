"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/option-chain/page",{

/***/ "(app-pages-browser)/./src/components/OptionChain.tsx":
/*!****************************************!*\
  !*** ./src/components/OptionChain.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptionChain; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction OptionChain(param) {\n    let { marketData } = param;\n    _s();\n    const [expiryData, setExpiryData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedExpiry, setSelectedExpiry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [optionChain, setOptionChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [niftySpotPrice, setNiftySpotPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // ✅ FIXED: Get NIFTY spot price from subscribed market data (security ID 13, exchange IDX_I)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // ✅ FIXED: Direct lookup for NIFTY spot (security ID 13) - should be subscribed with IDX_I exchange\n        const niftyData = marketData.get(\"13\");\n        if (niftyData && niftyData.ltp > 0) {\n            setNiftySpotPrice(niftyData.ltp);\n            console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Updated:\", niftyData.ltp, \"from security ID 13 (IDX_I)\");\n            return;\n        }\n        // ✅ ENHANCED: Fallback search for any NIFTY index data with better logging\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol === \"NIFTY\" && data.ltp > 0) {\n                setNiftySpotPrice(data.ltp);\n                console.log(\"[SPOT] \\uD83C\\uDFAF NIFTY Spot Price Found:\", data.ltp, \"from security:\", securityId, \"exchange:\", data.exchange);\n                return;\n            }\n        }\n        // ✅ FIXED: Only use mock price if absolutely no real data is available and log warning\n        if (niftySpotPrice === 0) {\n            const mockPrice = 24850;\n            setNiftySpotPrice(mockPrice);\n            console.warn(\"[SPOT] ⚠️ Using mock NIFTY Spot Price:\", mockPrice, \"(INDEX data not available - check subscription)\");\n        }\n    }, [\n        marketData\n    ]);\n    // Fetch expiry dates on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchExpiryDates();\n    }, []);\n    // Build option chain when expiry is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedExpiry && niftySpotPrice > 0) {\n            buildOptionChain();\n        }\n    }, [\n        selectedExpiry,\n        niftySpotPrice,\n        marketData\n    ]);\n    const fetchExpiryDates = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/nifty-expiry\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch expiry dates: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            if (result.success) {\n                setExpiryData(result.data);\n                // Auto-select first expiry\n                if (result.data.expiries.length > 0) {\n                    setSelectedExpiry(result.data.expiries[0]);\n                }\n            } else {\n                throw new Error(result.message || \"Failed to fetch expiry dates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching expiry dates:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const buildOptionChain = ()=>{\n        if (!selectedExpiry || niftySpotPrice <= 0) return;\n        console.log(\"\\uD83D\\uDD17 Building option chain for expiry:\", selectedExpiry, \"spot:\", niftySpotPrice);\n        // Generate strike prices around spot price\n        const strikes = generateStrikes(niftySpotPrice);\n        // Build option chain rows\n        const rows = strikes.map((strike)=>{\n            const callData = findOptionData(strike, \"CE\");\n            const putData = findOptionData(strike, \"PE\");\n            return {\n                strikePrice: strike,\n                call: callData,\n                put: putData\n            };\n        });\n        setOptionChain({\n            underlying: \"NIFTY\",\n            spotPrice: niftySpotPrice,\n            expiry: selectedExpiry,\n            rows,\n            timestamp: Date.now()\n        });\n    };\n    const generateStrikes = (spotPrice)=>{\n        // First, get all available strikes from market data for the selected expiry\n        const availableStrikes = new Set();\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            if (data.symbol.includes(\"NIFTY\") && data.symbol.includes(formatExpiryForSymbol(selectedExpiry))) {\n                // Extract strike from symbol (e.g., NIFTY-Jun2025-24850-CE)\n                const symbolParts = data.symbol.split(\"-\");\n                if (symbolParts.length >= 4) {\n                    const strike = parseFloat(symbolParts[2]);\n                    if (!isNaN(strike)) {\n                        availableStrikes.add(strike);\n                    }\n                }\n            }\n        }\n        if (availableStrikes.size === 0) {\n            // Fallback: generate strikes around spot price\n            const strikes = [];\n            const baseStrike = Math.round(spotPrice / 50) * 50;\n            for(let i = -10; i <= 10; i++){\n                strikes.push(baseStrike + i * 50);\n            }\n            return strikes.sort((a, b)=>a - b);\n        }\n        // Convert to sorted array\n        const allStrikes = Array.from(availableStrikes).sort((a, b)=>a - b);\n        // Find ATM strike (closest to spot price)\n        const atmStrike = allStrikes.reduce((prev, curr)=>Math.abs(curr - spotPrice) < Math.abs(prev - spotPrice) ? curr : prev);\n        console.log(\"[STRIKE] \\uD83C\\uDFAF ATM Strike identified: \".concat(atmStrike, \" (Spot: \").concat(spotPrice, \")\"));\n        console.log(\"[STRIKE] \\uD83D\\uDCCA Available strikes: \".concat(allStrikes.length));\n        // ✅ ENHANCED: Select more strikes around ATM (±12 strikes for better coverage)\n        const atmIndex = allStrikes.indexOf(atmStrike);\n        const selectedStrikes = [\n            ...allStrikes.slice(Math.max(0, atmIndex - 12), atmIndex),\n            atmStrike,\n            ...allStrikes.slice(atmIndex + 1, atmIndex + 13)\n        ];\n        console.log(\"[STRIKE] ✅ Selected \".concat(selectedStrikes.length, \" strikes around ATM:\"), selectedStrikes);\n        return selectedStrikes;\n    };\n    const findOptionData = (strike, optionType)=>{\n        // ✅ FIXED: Match by exact expiry date from CSV SM_EXPIRY_DATE column\n        for (const [securityId, data] of Array.from(marketData.entries())){\n            // Check if this is a NIFTY option with matching criteria\n            if (data.symbol.includes(\"NIFTY-\") && // Exact NIFTY options (not BANKNIFTY, etc.)\n            data.symbol.includes(\"-\".concat(strike, \"-\").concat(optionType)) && data.expiryDate === selectedExpiry) {\n                var _data_marketDepth_, _data_marketDepth, _data_marketDepth_1, _data_marketDepth1, _data_marketDepth_2, _data_marketDepth2, _data_marketDepth_3, _data_marketDepth3;\n                console.log(\"[OPTION] ✅ Found \".concat(optionType, \" \").concat(strike, \": \").concat(data.symbol, \" (Expiry: \").concat(data.expiryDate, \")\"));\n                return {\n                    securityId,\n                    symbol: data.symbol,\n                    exchange: data.exchange,\n                    strikePrice: strike,\n                    optionType,\n                    expiryDate: selectedExpiry,\n                    ltp: data.ltp || 0,\n                    change: data.change || 0,\n                    changePercent: data.changePercent || 0,\n                    volume: data.volume || 0,\n                    openInterest: data.openInterest,\n                    bid: ((_data_marketDepth = data.marketDepth) === null || _data_marketDepth === void 0 ? void 0 : (_data_marketDepth_ = _data_marketDepth[0]) === null || _data_marketDepth_ === void 0 ? void 0 : _data_marketDepth_.bidPrice) || data.bid,\n                    ask: ((_data_marketDepth1 = data.marketDepth) === null || _data_marketDepth1 === void 0 ? void 0 : (_data_marketDepth_1 = _data_marketDepth1[0]) === null || _data_marketDepth_1 === void 0 ? void 0 : _data_marketDepth_1.askPrice) || data.ask,\n                    bidQty: ((_data_marketDepth2 = data.marketDepth) === null || _data_marketDepth2 === void 0 ? void 0 : (_data_marketDepth_2 = _data_marketDepth2[0]) === null || _data_marketDepth_2 === void 0 ? void 0 : _data_marketDepth_2.bidQty) || data.bidQty,\n                    askQty: ((_data_marketDepth3 = data.marketDepth) === null || _data_marketDepth3 === void 0 ? void 0 : (_data_marketDepth_3 = _data_marketDepth3[0]) === null || _data_marketDepth_3 === void 0 ? void 0 : _data_marketDepth_3.askQty) || data.askQty,\n                    high: data.high,\n                    low: data.low,\n                    open: data.open,\n                    close: data.close,\n                    timestamp: data.timestamp\n                };\n            }\n        }\n        // Debug: Log when option not found\n        if (true) {\n            console.log(\"[OPTION] ❌ Not found \".concat(optionType, \" \").concat(strike, \" for expiry \").concat(selectedExpiry));\n            // Show available options for debugging\n            const availableOptions = Array.from(marketData.values()).filter((data)=>data.symbol.includes(\"NIFTY-\") && data.symbol.includes(\"-\".concat(optionType))).slice(0, 5);\n            console.log(\"[DEBUG] Available \".concat(optionType, \" options:\"), availableOptions.map((opt)=>\"\".concat(opt.symbol, \" (Expiry: \").concat(opt.expiryDate, \")\")));\n        }\n        return null;\n    };\n    const formatExpiryForSymbol = (expiry)=>{\n        // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)\n        const date = new Date(expiry);\n        const months = [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\",\n            \"Jul\",\n            \"Aug\",\n            \"Sep\",\n            \"Oct\",\n            \"Nov\",\n            \"Dec\"\n        ];\n        return \"\".concat(months[date.getMonth()]).concat(date.getFullYear());\n    };\n    const formatPrice = (price)=>{\n        if (!price || price <= 0) return \"N/A\";\n        return \"₹\".concat(price.toFixed(2));\n    };\n    const formatNumber = (num)=>{\n        if (!num) return \"N/A\";\n        return num.toLocaleString();\n    };\n    const getChangeColor = (change)=>{\n        if (!change) return \"text-gray-400\";\n        return change > 0 ? \"text-green-400\" : change < 0 ? \"text-red-400\" : \"text-gray-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading option chain...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-red-800 font-medium\",\n                    children: \"Error Loading Option Chain\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm mt-1\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchExpiryDates,\n                    className: \"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\",\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Options\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\",\n                                            children: \"\\uD83D\\uDD0D NIFTY\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Strategy builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Class\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md\",\n                                            children: \"Volatility\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"By expiration | by strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: formatPrice(niftySpotPrice)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        niftySpotPrice === 24850 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"(Mock)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            expiryData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n                        children: expiryData.expiries.slice(0, 15).map((expiry, index)=>{\n                            const date = new Date(expiry);\n                            const isSelected = expiry === selectedExpiry;\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isCurrentWeek = Math.abs(date.getTime() - new Date().getTime()) < 7 * 24 * 60 * 60 * 1000;\n                            // Enhanced date formatting\n                            const currentYear = new Date().getFullYear();\n                            const expiryYear = date.getFullYear();\n                            const monthShort = date.toLocaleDateString(\"en-US\", {\n                                month: \"short\"\n                            });\n                            const day = date.getDate();\n                            // Show year if different from current year\n                            const showYear = expiryYear !== currentYear;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedExpiry(expiry),\n                                className: \"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border \".concat(isSelected ? \"bg-black text-white border-black\" : isToday ? \"bg-orange-500 text-white border-orange-500\" : isCurrentWeek ? \"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center min-w-[40px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-normal text-gray-600\",\n                                            children: [\n                                                monthShort,\n                                                showYear && \" \".concat(expiryYear.toString().slice(-2))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-bold text-sm\",\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this)\n                            }, expiry, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-xs text-gray-500 bg-gray-100 p-3 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Selected Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        selectedExpiry\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Spot:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" ₹\",\n                                        niftySpotPrice,\n                                        \" \",\n                                        niftySpotPrice === 24850 ? \"(Mock)\" : \"(Live)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Available Strikes:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        (optionChain === null || optionChain === void 0 ? void 0 : optionChain.rows.length) || 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Market Data Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        marketData.size,\n                                        \" instruments\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"NIFTY Options:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\")).length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Options with Expiry:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" \",\n                                        Array.from(marketData.values()).filter((d)=>d.symbol.includes(\"NIFTY-\") && d.expiryDate).length,\n                                        \" matched\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-bold\",\n                                    children: \"Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-3 border-r border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold\",\n                                    children: \"Strike\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-bold\",\n                                    children: \"Puts\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1 border-r border-gray-200\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold\",\n                                children: \"Strike\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Security ID\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Ask\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Bid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 text-center py-2 px-1\",\n                                children: \"OI\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-100\",\n                        children: optionChain.rows.map((row, index)=>{\n                            var _row_call, _row_call1, _row_call2, _row_call3, _row_call4, _row_call5, _row_call6, _row_put, _row_put1, _row_put2, _row_put3, _row_put4, _row_put5, _row_put6;\n                            const isATM = Math.abs(row.strikePrice - niftySpotPrice) <= 50;\n                            const isITM_Call = row.strikePrice < niftySpotPrice;\n                            const isITM_Put = row.strikePrice > niftySpotPrice;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex hover:bg-gray-50 transition-colors \".concat(isATM ? \"bg-yellow-50\" : index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call = row.call) === null || _row_call === void 0 ? void 0 : _row_call.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Call ? \"text-green-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_call1 = row.call) === null || _row_call1 === void 0 ? void 0 : _row_call1.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_call2 = row.call) === null || _row_call2 === void 0 ? void 0 : _row_call2.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_call3 = row.call) === null || _row_call3 === void 0 ? void 0 : _row_call3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_call4 = row.call) === null || _row_call4 === void 0 ? void 0 : _row_call4.change)),\n                                        children: ((_row_call5 = row.call) === null || _row_call5 === void 0 ? void 0 : _row_call5.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.call.change > 0 ? \"+\" : \"\",\n                                                row.call.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.call.changePercent > 0 ? \"+\" : \"\",\n                                                        row.call.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isITM_Call ? \"text-green-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_call6 = row.call) === null || _row_call6 === void 0 ? void 0 : _row_call6.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 \".concat(isATM ? \"bg-yellow-100 text-yellow-800\" : \"text-gray-900\"),\n                                        children: row.strikePrice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-bold \".concat(isITM_Put ? \"text-red-600\" : \"text-gray-700\"),\n                                        children: formatPrice((_row_put = row.put) === null || _row_put === void 0 ? void 0 : _row_put.ltp)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm font-medium \".concat(getChangeColor((_row_put1 = row.put) === null || _row_put1 === void 0 ? void 0 : _row_put1.change)),\n                                        children: ((_row_put2 = row.put) === null || _row_put2 === void 0 ? void 0 : _row_put2.change) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                row.put.change > 0 ? \"+\" : \"\",\n                                                row.put.change.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"(\",\n                                                        row.put.changePercent > 0 ? \"+\" : \"\",\n                                                        row.put.changePercent.toFixed(1),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-red-600\",\n                                        children: formatPrice((_row_put3 = row.put) === null || _row_put3 === void 0 ? void 0 : _row_put3.ask)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm text-green-600\",\n                                        children: formatPrice((_row_put4 = row.put) === null || _row_put4 === void 0 ? void 0 : _row_put4.bid)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put5 = row.put) === null || _row_put5 === void 0 ? void 0 : _row_put5.volume)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-center py-3 px-1 text-sm \".concat(isITM_Put ? \"text-red-700 font-medium\" : \"text-gray-700\"),\n                                        children: formatNumber((_row_put6 = row.put) === null || _row_put6 === void 0 ? void 0 : _row_put6.openInterest)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, row.strikePrice, true, {\n                                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            optionChain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 border-t border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Show all\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Showing \",\n                                        optionChain.rows.length,\n                                        \" strikes around ATM\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                new Date(optionChain.timestamp).toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n                lineNumber: 503,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\like\\\\csv-market-dashboard\\\\src\\\\components\\\\OptionChain.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionChain, \"3MYzdkz2i0N1g/MoBahr6e4MTpU=\");\n_c = OptionChain;\nvar _c;\n$RefreshReg$(_c, \"OptionChain\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09wdGlvbkNoYWluLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEM7QUFPN0IsU0FBU0UsWUFBWSxLQUFnQztRQUFoQyxFQUFFQyxVQUFVLEVBQW9CLEdBQWhDOztJQUNsQyxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0wsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQ00sZ0JBQWdCQyxrQkFBa0IsR0FBR1AsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUF5QjtJQUN2RSxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNjLGdCQUFnQkMsa0JBQWtCLEdBQUdmLCtDQUFRQSxDQUFTO0lBRTdELDZGQUE2RjtJQUM3RkMsZ0RBQVNBLENBQUM7UUFDUixvR0FBb0c7UUFDcEcsTUFBTWUsWUFBWWIsV0FBV2MsR0FBRyxDQUFDO1FBQ2pDLElBQUlELGFBQWFBLFVBQVVFLEdBQUcsR0FBRyxHQUFHO1lBQ2xDSCxrQkFBa0JDLFVBQVVFLEdBQUc7WUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxpREFBdUNKLFVBQVVFLEdBQUcsRUFBRTtZQUNsRTtRQUNGO1FBRUEsMkVBQTJFO1FBQzNFLEtBQUssTUFBTSxDQUFDRyxZQUFZQyxLQUFLLElBQUlDLE1BQU1DLElBQUksQ0FBQ3JCLFdBQVdzQixPQUFPLElBQUs7WUFDakUsSUFBSUgsS0FBS0ksTUFBTSxLQUFLLFdBQVdKLEtBQUtKLEdBQUcsR0FBRyxHQUFHO2dCQUMzQ0gsa0JBQWtCTyxLQUFLSixHQUFHO2dCQUMxQkMsUUFBUUMsR0FBRyxDQUFDLCtDQUFxQ0UsS0FBS0osR0FBRyxFQUFFLGtCQUFrQkcsWUFBWSxhQUFhQyxLQUFLSyxRQUFRO2dCQUNuSDtZQUNGO1FBQ0Y7UUFFQSx1RkFBdUY7UUFDdkYsSUFBSWIsbUJBQW1CLEdBQUc7WUFDeEIsTUFBTWMsWUFBWTtZQUNsQmIsa0JBQWtCYTtZQUNsQlQsUUFBUVUsSUFBSSxDQUFDLDBDQUEwQ0QsV0FBVztRQUNwRTtJQUNGLEdBQUc7UUFBQ3pCO0tBQVc7SUFFZix3Q0FBd0M7SUFDeENGLGdEQUFTQSxDQUFDO1FBQ1I2QjtJQUNGLEdBQUcsRUFBRTtJQUVMLDZDQUE2QztJQUM3QzdCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSUssa0JBQWtCUSxpQkFBaUIsR0FBRztZQUN4Q2lCO1FBQ0Y7SUFDRixHQUFHO1FBQUN6QjtRQUFnQlE7UUFBZ0JYO0tBQVc7SUFFL0MsTUFBTTJCLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0ZuQixXQUFXO1lBQ1hFLFNBQVM7WUFFVCxNQUFNbUIsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0saUNBQXFELE9BQXBCSCxTQUFTSSxVQUFVO1lBQ3RFO1lBRUEsTUFBTUMsU0FBUyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2xDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJsQyxjQUFjZ0MsT0FBT2YsSUFBSTtnQkFDekIsMkJBQTJCO2dCQUMzQixJQUFJZSxPQUFPZixJQUFJLENBQUNrQixRQUFRLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUNuQ2xDLGtCQUFrQjhCLE9BQU9mLElBQUksQ0FBQ2tCLFFBQVEsQ0FBQyxFQUFFO2dCQUMzQztZQUNGLE9BQU87Z0JBQ0wsTUFBTSxJQUFJTCxNQUFNRSxPQUFPSyxPQUFPLElBQUk7WUFDcEM7UUFDRixFQUFFLE9BQU9DLEtBQUs7WUFDWnhCLFFBQVFQLEtBQUssQ0FBQyxnQ0FBZ0MrQjtZQUM5QzlCLFNBQVM4QixlQUFlUixRQUFRUSxJQUFJRCxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSL0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNb0IsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ3pCLGtCQUFrQlEsa0JBQWtCLEdBQUc7UUFFNUNLLFFBQVFDLEdBQUcsQ0FBQyxrREFBd0NkLGdCQUFnQixTQUFTUTtRQUU3RSwyQ0FBMkM7UUFDM0MsTUFBTThCLFVBQVVDLGdCQUFnQi9CO1FBRWhDLDBCQUEwQjtRQUMxQixNQUFNZ0MsT0FBeUJGLFFBQVFHLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDekMsTUFBTUMsV0FBV0MsZUFBZUYsUUFBUTtZQUN4QyxNQUFNRyxVQUFVRCxlQUFlRixRQUFRO1lBRXZDLE9BQU87Z0JBQ0xJLGFBQWFKO2dCQUNiSyxNQUFNSjtnQkFDTkssS0FBS0g7WUFDUDtRQUNGO1FBRUExQyxlQUFlO1lBQ2I4QyxZQUFZO1lBQ1pDLFdBQVcxQztZQUNYMkMsUUFBUW5EO1lBQ1J3QztZQUNBWSxXQUFXQyxLQUFLQyxHQUFHO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNZixrQkFBa0IsQ0FBQ1c7UUFDdkIsNEVBQTRFO1FBQzVFLE1BQU1LLG1CQUFtQixJQUFJQztRQUU3QixLQUFLLE1BQU0sQ0FBQ3pDLFlBQVlDLEtBQUssSUFBSUMsTUFBTUMsSUFBSSxDQUFDckIsV0FBV3NCLE9BQU8sSUFBSztZQUNqRSxJQUFJSCxLQUFLSSxNQUFNLENBQUNxQyxRQUFRLENBQUMsWUFDckJ6QyxLQUFLSSxNQUFNLENBQUNxQyxRQUFRLENBQUNDLHNCQUFzQjFELGtCQUFrQjtnQkFFL0QsNERBQTREO2dCQUM1RCxNQUFNMkQsY0FBYzNDLEtBQUtJLE1BQU0sQ0FBQ3dDLEtBQUssQ0FBQztnQkFDdEMsSUFBSUQsWUFBWXhCLE1BQU0sSUFBSSxHQUFHO29CQUMzQixNQUFNTyxTQUFTbUIsV0FBV0YsV0FBVyxDQUFDLEVBQUU7b0JBQ3hDLElBQUksQ0FBQ0csTUFBTXBCLFNBQVM7d0JBQ2xCYSxpQkFBaUJRLEdBQUcsQ0FBQ3JCO29CQUN2QjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJYSxpQkFBaUJTLElBQUksS0FBSyxHQUFHO1lBQy9CLCtDQUErQztZQUMvQyxNQUFNMUIsVUFBb0IsRUFBRTtZQUM1QixNQUFNMkIsYUFBYUMsS0FBS0MsS0FBSyxDQUFDakIsWUFBWSxNQUFNO1lBQ2hELElBQUssSUFBSWtCLElBQUksQ0FBQyxJQUFJQSxLQUFLLElBQUlBLElBQUs7Z0JBQzlCOUIsUUFBUStCLElBQUksQ0FBQ0osYUFBY0csSUFBSTtZQUNqQztZQUNBLE9BQU85QixRQUFRZ0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDO1FBQ3BDO1FBRUEsMEJBQTBCO1FBQzFCLE1BQU1DLGFBQWF4RCxNQUFNQyxJQUFJLENBQUNxQyxrQkFBa0JlLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUVuRSwwQ0FBMEM7UUFDMUMsTUFBTUUsWUFBWUQsV0FBV0UsTUFBTSxDQUFDLENBQUNDLE1BQU1DLE9BQ3pDWCxLQUFLWSxHQUFHLENBQUNELE9BQU8zQixhQUFhZ0IsS0FBS1ksR0FBRyxDQUFDRixPQUFPMUIsYUFBYTJCLE9BQU9EO1FBR25FL0QsUUFBUUMsR0FBRyxDQUFDLGdEQUEwRG9DLE9BQXBCd0IsV0FBVSxZQUFvQixPQUFWeEIsV0FBVTtRQUNoRnJDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBb0QsT0FBbEIyRCxXQUFXdEMsTUFBTTtRQUUvRCwrRUFBK0U7UUFDL0UsTUFBTTRDLFdBQVdOLFdBQVdPLE9BQU8sQ0FBQ047UUFDcEMsTUFBTU8sa0JBQWtCO2VBQ25CUixXQUFXUyxLQUFLLENBQUNoQixLQUFLaUIsR0FBRyxDQUFDLEdBQUdKLFdBQVcsS0FBS0E7WUFDaERMO2VBQ0dELFdBQVdTLEtBQUssQ0FBQ0gsV0FBVyxHQUFHQSxXQUFXO1NBQzlDO1FBRURsRSxRQUFRQyxHQUFHLENBQUMsdUJBQThDLE9BQXZCbUUsZ0JBQWdCOUMsTUFBTSxFQUFDLHlCQUF1QjhDO1FBRWpGLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNckMsaUJBQWlCLENBQUNGLFFBQWdCMEM7UUFDdEMscUVBQXFFO1FBQ3JFLEtBQUssTUFBTSxDQUFDckUsWUFBWUMsS0FBSyxJQUFJQyxNQUFNQyxJQUFJLENBQUNyQixXQUFXc0IsT0FBTyxJQUFLO1lBQ2pFLHlEQUF5RDtZQUN6RCxJQUFJSCxLQUFLSSxNQUFNLENBQUNxQyxRQUFRLENBQUMsYUFBYyw0Q0FBNEM7WUFDL0V6QyxLQUFLSSxNQUFNLENBQUNxQyxRQUFRLENBQUMsSUFBYzJCLE9BQVYxQyxRQUFPLEtBQWMsT0FBWDBDLGdCQUNuQ3BFLEtBQUtxRSxVQUFVLEtBQUtyRixnQkFBZ0I7b0JBZ0IvQmdCLG9CQUFBQSxtQkFDQUEscUJBQUFBLG9CQUNHQSxxQkFBQUEsb0JBQ0FBLHFCQUFBQTtnQkFqQlZILFFBQVFDLEdBQUcsQ0FBQyxvQkFBa0M0QixPQUFkMEMsWUFBVyxLQUFjcEUsT0FBWDBCLFFBQU8sTUFBNEIxQixPQUF4QkEsS0FBS0ksTUFBTSxFQUFDLGNBQTRCLE9BQWhCSixLQUFLcUUsVUFBVSxFQUFDO2dCQUVqRyxPQUFPO29CQUNMdEU7b0JBQ0FLLFFBQVFKLEtBQUtJLE1BQU07b0JBQ25CQyxVQUFVTCxLQUFLSyxRQUFRO29CQUN2QnlCLGFBQWFKO29CQUNiMEM7b0JBQ0FDLFlBQVlyRjtvQkFDWlksS0FBS0ksS0FBS0osR0FBRyxJQUFJO29CQUNqQjBFLFFBQVF0RSxLQUFLc0UsTUFBTSxJQUFJO29CQUN2QkMsZUFBZXZFLEtBQUt1RSxhQUFhLElBQUk7b0JBQ3JDQyxRQUFReEUsS0FBS3dFLE1BQU0sSUFBSTtvQkFDdkJDLGNBQWN6RSxLQUFLeUUsWUFBWTtvQkFDL0JDLEtBQUsxRSxFQUFBQSxvQkFBQUEsS0FBSzJFLFdBQVcsY0FBaEIzRSx5Q0FBQUEscUJBQUFBLGlCQUFrQixDQUFDLEVBQUUsY0FBckJBLHlDQUFBQSxtQkFBdUI0RSxRQUFRLEtBQUk1RSxLQUFLMEUsR0FBRztvQkFDaERHLEtBQUs3RSxFQUFBQSxxQkFBQUEsS0FBSzJFLFdBQVcsY0FBaEIzRSwwQ0FBQUEsc0JBQUFBLGtCQUFrQixDQUFDLEVBQUUsY0FBckJBLDBDQUFBQSxvQkFBdUI4RSxRQUFRLEtBQUk5RSxLQUFLNkUsR0FBRztvQkFDaERFLFFBQVEvRSxFQUFBQSxxQkFBQUEsS0FBSzJFLFdBQVcsY0FBaEIzRSwwQ0FBQUEsc0JBQUFBLGtCQUFrQixDQUFDLEVBQUUsY0FBckJBLDBDQUFBQSxvQkFBdUIrRSxNQUFNLEtBQUkvRSxLQUFLK0UsTUFBTTtvQkFDcERDLFFBQVFoRixFQUFBQSxxQkFBQUEsS0FBSzJFLFdBQVcsY0FBaEIzRSwwQ0FBQUEsc0JBQUFBLGtCQUFrQixDQUFDLEVBQUUsY0FBckJBLDBDQUFBQSxvQkFBdUJnRixNQUFNLEtBQUloRixLQUFLZ0YsTUFBTTtvQkFDcERDLE1BQU1qRixLQUFLaUYsSUFBSTtvQkFDZkMsS0FBS2xGLEtBQUtrRixHQUFHO29CQUNiQyxNQUFNbkYsS0FBS21GLElBQUk7b0JBQ2ZDLE9BQU9wRixLQUFLb0YsS0FBSztvQkFDakJoRCxXQUFXcEMsS0FBS29DLFNBQVM7Z0JBQzNCO1lBQ0Y7UUFDRjtRQUVBLG1DQUFtQztRQUNuQyxJQUFJaUQsSUFBeUIsRUFBZTtZQUMxQ3hGLFFBQVFDLEdBQUcsQ0FBQyx3QkFBc0M0QixPQUFkMEMsWUFBVyxLQUF3QnBGLE9BQXJCMEMsUUFBTyxnQkFBNkIsT0FBZjFDO1lBRXZFLHVDQUF1QztZQUN2QyxNQUFNc0csbUJBQW1CckYsTUFBTUMsSUFBSSxDQUFDckIsV0FBVzBHLE1BQU0sSUFDbERDLE1BQU0sQ0FBQ3hGLENBQUFBLE9BQVFBLEtBQUtJLE1BQU0sQ0FBQ3FDLFFBQVEsQ0FBQyxhQUFhekMsS0FBS0ksTUFBTSxDQUFDcUMsUUFBUSxDQUFDLElBQWUsT0FBWDJCLGNBQzFFRixLQUFLLENBQUMsR0FBRztZQUVackUsUUFBUUMsR0FBRyxDQUFDLHFCQUFnQyxPQUFYc0UsWUFBVyxjQUFZa0IsaUJBQWlCN0QsR0FBRyxDQUFDZ0UsQ0FBQUEsTUFDM0UsR0FBMEJBLE9BQXZCQSxJQUFJckYsTUFBTSxFQUFDLGNBQTJCLE9BQWZxRixJQUFJcEIsVUFBVSxFQUFDO1FBRTdDO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTTNCLHdCQUF3QixDQUFDUDtRQUM3QiwrREFBK0Q7UUFDL0QsTUFBTXVELE9BQU8sSUFBSXJELEtBQUtGO1FBQ3RCLE1BQU13RCxTQUFTO1lBQUM7WUFBTztZQUFPO1lBQU87WUFBTztZQUFPO1lBQ3BDO1lBQU87WUFBTztZQUFPO1lBQU87WUFBTztTQUFNO1FBQ3hELE9BQU8sR0FBNkJELE9BQTFCQyxNQUFNLENBQUNELEtBQUtFLFFBQVEsR0FBRyxFQUFzQixPQUFuQkYsS0FBS0csV0FBVztJQUN0RDtJQUVBLE1BQU1DLGNBQWMsQ0FBQ0M7UUFDbkIsSUFBSSxDQUFDQSxTQUFTQSxTQUFTLEdBQUcsT0FBTztRQUNqQyxPQUFPLElBQXFCLE9BQWpCQSxNQUFNQyxPQUFPLENBQUM7SUFDM0I7SUFFQSxNQUFNQyxlQUFlLENBQUNDO1FBQ3BCLElBQUksQ0FBQ0EsS0FBSyxPQUFPO1FBQ2pCLE9BQU9BLElBQUlDLGNBQWM7SUFDM0I7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQzlCO1FBQ3RCLElBQUksQ0FBQ0EsUUFBUSxPQUFPO1FBQ3BCLE9BQU9BLFNBQVMsSUFBSSxtQkFBbUJBLFNBQVMsSUFBSSxpQkFBaUI7SUFDdkU7SUFFQSxJQUFJbEYsU0FBUztRQUNYLHFCQUNFLDhEQUFDaUg7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsSUFBSWhILE9BQU87UUFDVCxxQkFDRSw4REFBQytHO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRTtvQkFBR0YsV0FBVTs4QkFBMkI7Ozs7Ozs4QkFDekMsOERBQUNDO29CQUFFRCxXQUFVOzhCQUE2QmhIOzs7Ozs7OEJBQzFDLDhEQUFDbUg7b0JBQ0NDLFNBQVNsRztvQkFDVDhGLFdBQVU7OEJBQ1g7Ozs7Ozs7Ozs7OztJQUtQO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNLO29DQUFHTCxXQUFVOzhDQUFtQzs7Ozs7OzhDQUdqRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRzs0Q0FBT0gsV0FBVTtzREFBb0U7Ozs7OztzREFHdEYsOERBQUNHOzRDQUFPSCxXQUFVO3NEQUE2RTs7Ozs7O3NEQUcvRiw4REFBQ0c7NENBQU9ILFdBQVU7c0RBQTZFOzs7Ozs7c0RBRy9GLDhEQUFDRzs0Q0FBT0gsV0FBVTtzREFBNkU7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPbkcsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7OENBR3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNNOzRDQUFLTixXQUFVO3NEQUF3Qjs7Ozs7O3NEQUN4Qyw4REFBQ007NENBQUtOLFdBQVU7c0RBQ2JSLFlBQVl0Rzs7Ozs7O3dDQUVkQSxtQkFBbUIsdUJBQ2xCLDhEQUFDb0g7NENBQUtOLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFqRHhILDRCQUNDLDhEQUFDdUg7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWnhILFdBQVdvQyxRQUFRLENBQUNnRCxLQUFLLENBQUMsR0FBRyxJQUFJekMsR0FBRyxDQUFDLENBQUNVLFFBQVEwRTs0QkFDN0MsTUFBTW5CLE9BQU8sSUFBSXJELEtBQUtGOzRCQUN0QixNQUFNMkUsYUFBYTNFLFdBQVduRDs0QkFDOUIsTUFBTStILFVBQVVyQixLQUFLc0IsWUFBWSxPQUFPLElBQUkzRSxPQUFPMkUsWUFBWTs0QkFDL0QsTUFBTUMsZ0JBQWdCL0QsS0FBS1ksR0FBRyxDQUFDNEIsS0FBS3dCLE9BQU8sS0FBSyxJQUFJN0UsT0FBTzZFLE9BQU8sTUFBTSxJQUFJLEtBQUssS0FBSyxLQUFLOzRCQUUzRiwyQkFBMkI7NEJBQzNCLE1BQU1DLGNBQWMsSUFBSTlFLE9BQU93RCxXQUFXOzRCQUMxQyxNQUFNdUIsYUFBYTFCLEtBQUtHLFdBQVc7NEJBQ25DLE1BQU13QixhQUFhM0IsS0FBSzRCLGtCQUFrQixDQUFDLFNBQVM7Z0NBQUVDLE9BQU87NEJBQVE7NEJBQ3JFLE1BQU1DLE1BQU05QixLQUFLK0IsT0FBTzs0QkFFeEIsMkNBQTJDOzRCQUMzQyxNQUFNQyxXQUFXTixlQUFlRDs0QkFFaEMscUJBQ0UsOERBQUNWO2dDQUVDQyxTQUFTLElBQU16SCxrQkFBa0JrRDtnQ0FDakNtRSxXQUFXLHdFQVFWLE9BUENRLGFBQ0kscUNBQ0FDLFVBQ0EsK0NBQ0FFLGdCQUNBLGdFQUNBOzBDQUdOLDRFQUFDWjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFDWmU7Z0RBQVlLLFlBQVksSUFBb0MsT0FBaENOLFdBQVdPLFFBQVEsR0FBR3pELEtBQUssQ0FBQyxDQUFDOzs7Ozs7O3NEQUU1RCw4REFBQ21DOzRDQUFJQyxXQUFVO3NEQUNaa0I7Ozs7Ozs7Ozs7OzsrQkFqQkFyRjs7Ozs7d0JBc0JYOzs7Ozs7b0JBaldaLEtBcVdvQyxrQkFDeEIsOERBQUNrRTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUF5Qjt3Q0FBRTVJOzs7Ozs7OzhDQUN4Qyw4REFBQ3FIOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQW9CO3dDQUFHcEk7d0NBQWU7d0NBQUVBLG1CQUFtQixRQUFRLFdBQVc7Ozs7Ozs7OENBQzNGLDhEQUFDNkc7O3NEQUFJLDhEQUFDdUI7c0RBQU87Ozs7Ozt3Q0FBMkI7d0NBQUUxSSxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFzQyxJQUFJLENBQUNMLE1BQU0sS0FBSTs7Ozs7Ozs4Q0FDdEUsOERBQUNrRjs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUEwQjt3Q0FBRS9JLFdBQVdtRSxJQUFJO3dDQUFDOzs7Ozs7OzhDQUN6RCw4REFBQ3FEOztzREFBSSw4REFBQ3VCO3NEQUFPOzs7Ozs7d0NBQXVCO3dDQUFFM0gsTUFBTUMsSUFBSSxDQUFDckIsV0FBVzBHLE1BQU0sSUFBSUMsTUFBTSxDQUFDcUMsQ0FBQUEsSUFBS0EsRUFBRXpILE1BQU0sQ0FBQ3FDLFFBQVEsQ0FBQyxXQUFXdEIsTUFBTTt3Q0FBQzs7Ozs7Ozs4Q0FDdEgsOERBQUNrRjs7c0RBQUksOERBQUN1QjtzREFBTzs7Ozs7O3dDQUE2Qjt3Q0FBRTNILE1BQU1DLElBQUksQ0FBQ3JCLFdBQVcwRyxNQUFNLElBQUlDLE1BQU0sQ0FBQ3FDLENBQUFBLElBQUtBLEVBQUV6SCxNQUFNLENBQUNxQyxRQUFRLENBQUMsYUFBYW9GLEVBQUV4RCxVQUFVLEVBQUVsRCxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRckpqQyw2QkFDQyw4REFBQ21IO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ007b0NBQUtOLFdBQVU7OENBQTJCOzs7Ozs7Ozs7OzswQ0FHN0MsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDTTtvQ0FBS04sV0FBVTs4Q0FBWTs7Ozs7Ozs7Ozs7MENBRzlCLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ007b0NBQUtOLFdBQVU7OENBQXlCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLN0MsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQXdEOzs7Ozs7MENBR3ZFLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBZ0U7Ozs7OzswQ0FHL0UsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7OzBDQUM5Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQStCOzs7Ozs7MENBQzlDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FBK0I7Ozs7OzswQ0FDOUMsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUErQjs7Ozs7Ozs7Ozs7O2tDQUloRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pwSCxZQUFZc0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQ3FHLEtBQUtqQjtnQ0FjTmlCLFdBR0FBLFlBR0RBLFlBR0FBLFlBRW9FQSxZQUNoRkEsWUFZWUEsWUFjQUEsVUFFb0VBLFdBQ2hGQSxXQVVZQSxXQUdBQSxXQUdDQSxXQUdBQTs0QkF6RXBCLE1BQU1DLFFBQVE3RSxLQUFLWSxHQUFHLENBQUNnRSxJQUFJaEcsV0FBVyxHQUFHdEMsbUJBQW1COzRCQUM1RCxNQUFNd0ksYUFBYUYsSUFBSWhHLFdBQVcsR0FBR3RDOzRCQUNyQyxNQUFNeUksWUFBWUgsSUFBSWhHLFdBQVcsR0FBR3RDOzRCQUVwQyxxQkFDRSw4REFBQzZHO2dDQUVDQyxXQUFXLDJDQUVWLE9BREN5QixRQUFRLGlCQUFpQmxCLFFBQVEsTUFBTSxJQUFJLGFBQWE7O2tEQUkxRCw4REFBQ1I7d0NBQUlDLFdBQVcsd0NBQW9HLE9BQTVEMEIsYUFBYSwrQkFBK0I7a0RBQ2pHL0IsY0FBYTZCLFlBQUFBLElBQUkvRixJQUFJLGNBQVIrRixnQ0FBQUEsVUFBVXJELFlBQVk7Ozs7OztrREFFdEMsOERBQUM0Qjt3Q0FBSUMsV0FBVyx3Q0FBb0csT0FBNUQwQixhQUFhLCtCQUErQjtrREFDakcvQixjQUFhNkIsYUFBQUEsSUFBSS9GLElBQUksY0FBUitGLGlDQUFBQSxXQUFVdEQsTUFBTTs7Ozs7O2tEQUVoQyw4REFBQzZCO3dDQUFJQyxXQUFVO2tEQUNaUixhQUFZZ0MsYUFBQUEsSUFBSS9GLElBQUksY0FBUitGLGlDQUFBQSxXQUFVcEQsR0FBRzs7Ozs7O2tEQUU1Qiw4REFBQzJCO3dDQUFJQyxXQUFVO2tEQUNaUixhQUFZZ0MsYUFBQUEsSUFBSS9GLElBQUksY0FBUitGLGlDQUFBQSxXQUFVakQsR0FBRzs7Ozs7O2tEQUU1Qiw4REFBQ3dCO3dDQUFJQyxXQUFXLG9EQUFxRixPQUFqQ0YsZ0JBQWUwQixhQUFBQSxJQUFJL0YsSUFBSSxjQUFSK0YsaUNBQUFBLFdBQVV4RCxNQUFNO2tEQUNoR3dELEVBQUFBLGFBQUFBLElBQUkvRixJQUFJLGNBQVIrRixpQ0FBQUEsV0FBVXhELE1BQU0sa0JBQ2Y7O2dEQUNHd0QsSUFBSS9GLElBQUksQ0FBQ3VDLE1BQU0sR0FBRyxJQUFJLE1BQU07Z0RBQUl3RCxJQUFJL0YsSUFBSSxDQUFDdUMsTUFBTSxDQUFDMEIsT0FBTyxDQUFDOzhEQUN6RCw4REFBQ0s7b0RBQUlDLFdBQVU7O3dEQUFVO3dEQUNyQndCLElBQUkvRixJQUFJLENBQUN3QyxhQUFhLEdBQUcsSUFBSSxNQUFNO3dEQUFJdUQsSUFBSS9GLElBQUksQ0FBQ3dDLGFBQWEsQ0FBQ3lCLE9BQU8sQ0FBQzt3REFBRzs7Ozs7Ozs7MkRBRzdFOzs7Ozs7a0RBRU4sOERBQUNLO3dDQUFJQyxXQUFXLDJFQUVmLE9BREMwQixhQUFhLG1CQUFtQjtrREFFL0JsQyxhQUFZZ0MsYUFBQUEsSUFBSS9GLElBQUksY0FBUitGLGlDQUFBQSxXQUFVbEksR0FBRzs7Ozs7O2tEQUk1Qiw4REFBQ3lHO3dDQUFJQyxXQUFXLHlFQUVmLE9BREN5QixRQUFRLGtDQUFrQztrREFFekNELElBQUloRyxXQUFXOzs7Ozs7a0RBSWxCLDhEQUFDdUU7d0NBQUlDLFdBQVcsa0RBRWYsT0FEQzJCLFlBQVksaUJBQWlCO2tEQUU1Qm5DLGFBQVlnQyxXQUFBQSxJQUFJOUYsR0FBRyxjQUFQOEYsK0JBQUFBLFNBQVNsSSxHQUFHOzs7Ozs7a0RBRTNCLDhEQUFDeUc7d0NBQUlDLFdBQVcsb0RBQW9GLE9BQWhDRixnQkFBZTBCLFlBQUFBLElBQUk5RixHQUFHLGNBQVA4RixnQ0FBQUEsVUFBU3hELE1BQU07a0RBQy9Gd0QsRUFBQUEsWUFBQUEsSUFBSTlGLEdBQUcsY0FBUDhGLGdDQUFBQSxVQUFTeEQsTUFBTSxrQkFDZDs7Z0RBQ0d3RCxJQUFJOUYsR0FBRyxDQUFDc0MsTUFBTSxHQUFHLElBQUksTUFBTTtnREFBSXdELElBQUk5RixHQUFHLENBQUNzQyxNQUFNLENBQUMwQixPQUFPLENBQUM7OERBQ3ZELDhEQUFDSztvREFBSUMsV0FBVTs7d0RBQVU7d0RBQ3JCd0IsSUFBSTlGLEdBQUcsQ0FBQ3VDLGFBQWEsR0FBRyxJQUFJLE1BQU07d0RBQUl1RCxJQUFJOUYsR0FBRyxDQUFDdUMsYUFBYSxDQUFDeUIsT0FBTyxDQUFDO3dEQUFHOzs7Ozs7OzsyREFHM0U7Ozs7OztrREFFTiw4REFBQ0s7d0NBQUlDLFdBQVU7a0RBQ1pSLGFBQVlnQyxZQUFBQSxJQUFJOUYsR0FBRyxjQUFQOEYsZ0NBQUFBLFVBQVNqRCxHQUFHOzs7Ozs7a0RBRTNCLDhEQUFDd0I7d0NBQUlDLFdBQVU7a0RBQ1pSLGFBQVlnQyxZQUFBQSxJQUFJOUYsR0FBRyxjQUFQOEYsZ0NBQUFBLFVBQVNwRCxHQUFHOzs7Ozs7a0RBRTNCLDhEQUFDMkI7d0NBQUlDLFdBQVcsd0NBQWlHLE9BQXpEMkIsWUFBWSw2QkFBNkI7a0RBQzlGaEMsY0FBYTZCLFlBQUFBLElBQUk5RixHQUFHLGNBQVA4RixnQ0FBQUEsVUFBU3RELE1BQU07Ozs7OztrREFFL0IsOERBQUM2Qjt3Q0FBSUMsV0FBVyx3Q0FBaUcsT0FBekQyQixZQUFZLDZCQUE2QjtrREFDOUZoQyxjQUFhNkIsWUFBQUEsSUFBSTlGLEdBQUcsY0FBUDhGLGdDQUFBQSxVQUFTckQsWUFBWTs7Ozs7OzsrQkFuRWhDcUQsSUFBSWhHLFdBQVc7Ozs7O3dCQXVFMUI7Ozs7Ozs7Ozs7OztZQU1MNUMsNkJBQ0MsOERBQUNtSDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNNOzhDQUFLOzs7Ozs7OENBQ04sOERBQUNBOzhDQUFLOzs7Ozs7OENBQ04sOERBQUNBOzt3Q0FBSzt3Q0FBUzFILFlBQVlzQyxJQUFJLENBQUNMLE1BQU07d0NBQUM7Ozs7Ozs7Ozs7Ozs7c0NBRXpDLDhEQUFDa0Y7NEJBQUlDLFdBQVU7O2dDQUF3QjtnQ0FDdEIsSUFBSWpFLEtBQUtuRCxZQUFZa0QsU0FBUyxFQUFFOEYsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0U7R0E1ZndCdEo7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvT3B0aW9uQ2hhaW4udHN4PzVjODEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgT3B0aW9uQ2hhaW5EYXRhLCBPcHRpb25DaGFpblJvdywgT3B0aW9uRGF0YSwgRXhwaXJ5RGF0YSwgTWFya2V0RGF0YSB9IGZyb20gJy4uL3R5cGVzJztcblxuaW50ZXJmYWNlIE9wdGlvbkNoYWluUHJvcHMge1xuICBtYXJrZXREYXRhOiBNYXA8c3RyaW5nLCBNYXJrZXREYXRhPjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gT3B0aW9uQ2hhaW4oeyBtYXJrZXREYXRhIH06IE9wdGlvbkNoYWluUHJvcHMpIHtcbiAgY29uc3QgW2V4cGlyeURhdGEsIHNldEV4cGlyeURhdGFdID0gdXNlU3RhdGU8RXhwaXJ5RGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRFeHBpcnksIHNldFNlbGVjdGVkRXhwaXJ5XSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbb3B0aW9uQ2hhaW4sIHNldE9wdGlvbkNoYWluXSA9IHVzZVN0YXRlPE9wdGlvbkNoYWluRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtuaWZ0eVNwb3RQcmljZSwgc2V0TmlmdHlTcG90UHJpY2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcblxuICAvLyDinIUgRklYRUQ6IEdldCBOSUZUWSBzcG90IHByaWNlIGZyb20gc3Vic2NyaWJlZCBtYXJrZXQgZGF0YSAoc2VjdXJpdHkgSUQgMTMsIGV4Y2hhbmdlIElEWF9JKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIOKchSBGSVhFRDogRGlyZWN0IGxvb2t1cCBmb3IgTklGVFkgc3BvdCAoc2VjdXJpdHkgSUQgMTMpIC0gc2hvdWxkIGJlIHN1YnNjcmliZWQgd2l0aCBJRFhfSSBleGNoYW5nZVxuICAgIGNvbnN0IG5pZnR5RGF0YSA9IG1hcmtldERhdGEuZ2V0KCcxMycpO1xuICAgIGlmIChuaWZ0eURhdGEgJiYgbmlmdHlEYXRhLmx0cCA+IDApIHtcbiAgICAgIHNldE5pZnR5U3BvdFByaWNlKG5pZnR5RGF0YS5sdHApO1xuICAgICAgY29uc29sZS5sb2coJ1tTUE9UXSDwn46vIE5JRlRZIFNwb3QgUHJpY2UgVXBkYXRlZDonLCBuaWZ0eURhdGEubHRwLCAnZnJvbSBzZWN1cml0eSBJRCAxMyAoSURYX0kpJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g4pyFIEVOSEFOQ0VEOiBGYWxsYmFjayBzZWFyY2ggZm9yIGFueSBOSUZUWSBpbmRleCBkYXRhIHdpdGggYmV0dGVyIGxvZ2dpbmdcbiAgICBmb3IgKGNvbnN0IFtzZWN1cml0eUlkLCBkYXRhXSBvZiBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKSkge1xuICAgICAgaWYgKGRhdGEuc3ltYm9sID09PSAnTklGVFknICYmIGRhdGEubHRwID4gMCkge1xuICAgICAgICBzZXROaWZ0eVNwb3RQcmljZShkYXRhLmx0cCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbU1BPVF0g8J+OryBOSUZUWSBTcG90IFByaWNlIEZvdW5kOicsIGRhdGEubHRwLCAnZnJvbSBzZWN1cml0eTonLCBzZWN1cml0eUlkLCAnZXhjaGFuZ2U6JywgZGF0YS5leGNoYW5nZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDinIUgRklYRUQ6IE9ubHkgdXNlIG1vY2sgcHJpY2UgaWYgYWJzb2x1dGVseSBubyByZWFsIGRhdGEgaXMgYXZhaWxhYmxlIGFuZCBsb2cgd2FybmluZ1xuICAgIGlmIChuaWZ0eVNwb3RQcmljZSA9PT0gMCkge1xuICAgICAgY29uc3QgbW9ja1ByaWNlID0gMjQ4NTA7XG4gICAgICBzZXROaWZ0eVNwb3RQcmljZShtb2NrUHJpY2UpO1xuICAgICAgY29uc29sZS53YXJuKCdbU1BPVF0g4pqg77iPIFVzaW5nIG1vY2sgTklGVFkgU3BvdCBQcmljZTonLCBtb2NrUHJpY2UsICcoSU5ERVggZGF0YSBub3QgYXZhaWxhYmxlIC0gY2hlY2sgc3Vic2NyaXB0aW9uKScpO1xuICAgIH1cbiAgfSwgW21hcmtldERhdGFdKTtcblxuICAvLyBGZXRjaCBleHBpcnkgZGF0ZXMgb24gY29tcG9uZW50IG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hFeHBpcnlEYXRlcygpO1xuICB9LCBbXSk7XG5cbiAgLy8gQnVpbGQgb3B0aW9uIGNoYWluIHdoZW4gZXhwaXJ5IGlzIHNlbGVjdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkRXhwaXJ5ICYmIG5pZnR5U3BvdFByaWNlID4gMCkge1xuICAgICAgYnVpbGRPcHRpb25DaGFpbigpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkRXhwaXJ5LCBuaWZ0eVNwb3RQcmljZSwgbWFya2V0RGF0YV0pO1xuXG4gIGNvbnN0IGZldGNoRXhwaXJ5RGF0ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9uaWZ0eS1leHBpcnknKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggZXhwaXJ5IGRhdGVzOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRFeHBpcnlEYXRhKHJlc3VsdC5kYXRhKTtcbiAgICAgICAgLy8gQXV0by1zZWxlY3QgZmlyc3QgZXhwaXJ5XG4gICAgICAgIGlmIChyZXN1bHQuZGF0YS5leHBpcmllcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRFeHBpcnkocmVzdWx0LmRhdGEuZXhwaXJpZXNbMF0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0Lm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBmZXRjaCBleHBpcnkgZGF0ZXMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGV4cGlyeSBkYXRlczonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBidWlsZE9wdGlvbkNoYWluID0gKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRFeHBpcnkgfHwgbmlmdHlTcG90UHJpY2UgPD0gMCkgcmV0dXJuO1xuXG4gICAgY29uc29sZS5sb2coJ/CflJcgQnVpbGRpbmcgb3B0aW9uIGNoYWluIGZvciBleHBpcnk6Jywgc2VsZWN0ZWRFeHBpcnksICdzcG90OicsIG5pZnR5U3BvdFByaWNlKTtcblxuICAgIC8vIEdlbmVyYXRlIHN0cmlrZSBwcmljZXMgYXJvdW5kIHNwb3QgcHJpY2VcbiAgICBjb25zdCBzdHJpa2VzID0gZ2VuZXJhdGVTdHJpa2VzKG5pZnR5U3BvdFByaWNlKTtcbiAgICBcbiAgICAvLyBCdWlsZCBvcHRpb24gY2hhaW4gcm93c1xuICAgIGNvbnN0IHJvd3M6IE9wdGlvbkNoYWluUm93W10gPSBzdHJpa2VzLm1hcChzdHJpa2UgPT4ge1xuICAgICAgY29uc3QgY2FsbERhdGEgPSBmaW5kT3B0aW9uRGF0YShzdHJpa2UsICdDRScpO1xuICAgICAgY29uc3QgcHV0RGF0YSA9IGZpbmRPcHRpb25EYXRhKHN0cmlrZSwgJ1BFJyk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0cmlrZVByaWNlOiBzdHJpa2UsXG4gICAgICAgIGNhbGw6IGNhbGxEYXRhLFxuICAgICAgICBwdXQ6IHB1dERhdGFcbiAgICAgIH07XG4gICAgfSk7XG5cbiAgICBzZXRPcHRpb25DaGFpbih7XG4gICAgICB1bmRlcmx5aW5nOiAnTklGVFknLFxuICAgICAgc3BvdFByaWNlOiBuaWZ0eVNwb3RQcmljZSxcbiAgICAgIGV4cGlyeTogc2VsZWN0ZWRFeHBpcnksXG4gICAgICByb3dzLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVTdHJpa2VzID0gKHNwb3RQcmljZTogbnVtYmVyKTogbnVtYmVyW10gPT4ge1xuICAgIC8vIEZpcnN0LCBnZXQgYWxsIGF2YWlsYWJsZSBzdHJpa2VzIGZyb20gbWFya2V0IGRhdGEgZm9yIHRoZSBzZWxlY3RlZCBleHBpcnlcbiAgICBjb25zdCBhdmFpbGFibGVTdHJpa2VzID0gbmV3IFNldDxudW1iZXI+KCk7XG5cbiAgICBmb3IgKGNvbnN0IFtzZWN1cml0eUlkLCBkYXRhXSBvZiBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKSkge1xuICAgICAgaWYgKGRhdGEuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWScpICYmXG4gICAgICAgICAgZGF0YS5zeW1ib2wuaW5jbHVkZXMoZm9ybWF0RXhwaXJ5Rm9yU3ltYm9sKHNlbGVjdGVkRXhwaXJ5KSkpIHtcblxuICAgICAgICAvLyBFeHRyYWN0IHN0cmlrZSBmcm9tIHN5bWJvbCAoZS5nLiwgTklGVFktSnVuMjAyNS0yNDg1MC1DRSlcbiAgICAgICAgY29uc3Qgc3ltYm9sUGFydHMgPSBkYXRhLnN5bWJvbC5zcGxpdCgnLScpO1xuICAgICAgICBpZiAoc3ltYm9sUGFydHMubGVuZ3RoID49IDQpIHtcbiAgICAgICAgICBjb25zdCBzdHJpa2UgPSBwYXJzZUZsb2F0KHN5bWJvbFBhcnRzWzJdKTtcbiAgICAgICAgICBpZiAoIWlzTmFOKHN0cmlrZSkpIHtcbiAgICAgICAgICAgIGF2YWlsYWJsZVN0cmlrZXMuYWRkKHN0cmlrZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGF2YWlsYWJsZVN0cmlrZXMuc2l6ZSA9PT0gMCkge1xuICAgICAgLy8gRmFsbGJhY2s6IGdlbmVyYXRlIHN0cmlrZXMgYXJvdW5kIHNwb3QgcHJpY2VcbiAgICAgIGNvbnN0IHN0cmlrZXM6IG51bWJlcltdID0gW107XG4gICAgICBjb25zdCBiYXNlU3RyaWtlID0gTWF0aC5yb3VuZChzcG90UHJpY2UgLyA1MCkgKiA1MDtcbiAgICAgIGZvciAobGV0IGkgPSAtMTA7IGkgPD0gMTA7IGkrKykge1xuICAgICAgICBzdHJpa2VzLnB1c2goYmFzZVN0cmlrZSArIChpICogNTApKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzdHJpa2VzLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcbiAgICB9XG5cbiAgICAvLyBDb252ZXJ0IHRvIHNvcnRlZCBhcnJheVxuICAgIGNvbnN0IGFsbFN0cmlrZXMgPSBBcnJheS5mcm9tKGF2YWlsYWJsZVN0cmlrZXMpLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcblxuICAgIC8vIEZpbmQgQVRNIHN0cmlrZSAoY2xvc2VzdCB0byBzcG90IHByaWNlKVxuICAgIGNvbnN0IGF0bVN0cmlrZSA9IGFsbFN0cmlrZXMucmVkdWNlKChwcmV2LCBjdXJyKSA9PlxuICAgICAgTWF0aC5hYnMoY3VyciAtIHNwb3RQcmljZSkgPCBNYXRoLmFicyhwcmV2IC0gc3BvdFByaWNlKSA/IGN1cnIgOiBwcmV2XG4gICAgKTtcblxuICAgIGNvbnNvbGUubG9nKGBbU1RSSUtFXSDwn46vIEFUTSBTdHJpa2UgaWRlbnRpZmllZDogJHthdG1TdHJpa2V9IChTcG90OiAke3Nwb3RQcmljZX0pYCk7XG4gICAgY29uc29sZS5sb2coYFtTVFJJS0VdIPCfk4ogQXZhaWxhYmxlIHN0cmlrZXM6ICR7YWxsU3RyaWtlcy5sZW5ndGh9YCk7XG5cbiAgICAvLyDinIUgRU5IQU5DRUQ6IFNlbGVjdCBtb3JlIHN0cmlrZXMgYXJvdW5kIEFUTSAowrExMiBzdHJpa2VzIGZvciBiZXR0ZXIgY292ZXJhZ2UpXG4gICAgY29uc3QgYXRtSW5kZXggPSBhbGxTdHJpa2VzLmluZGV4T2YoYXRtU3RyaWtlKTtcbiAgICBjb25zdCBzZWxlY3RlZFN0cmlrZXMgPSBbXG4gICAgICAuLi5hbGxTdHJpa2VzLnNsaWNlKE1hdGgubWF4KDAsIGF0bUluZGV4IC0gMTIpLCBhdG1JbmRleCksXG4gICAgICBhdG1TdHJpa2UsXG4gICAgICAuLi5hbGxTdHJpa2VzLnNsaWNlKGF0bUluZGV4ICsgMSwgYXRtSW5kZXggKyAxMylcbiAgICBdO1xuXG4gICAgY29uc29sZS5sb2coYFtTVFJJS0VdIOKchSBTZWxlY3RlZCAke3NlbGVjdGVkU3RyaWtlcy5sZW5ndGh9IHN0cmlrZXMgYXJvdW5kIEFUTTpgLCBzZWxlY3RlZFN0cmlrZXMpO1xuXG4gICAgcmV0dXJuIHNlbGVjdGVkU3RyaWtlcztcbiAgfTtcblxuICBjb25zdCBmaW5kT3B0aW9uRGF0YSA9IChzdHJpa2U6IG51bWJlciwgb3B0aW9uVHlwZTogJ0NFJyB8ICdQRScpOiBPcHRpb25EYXRhIHwgbnVsbCA9PiB7XG4gICAgLy8g4pyFIEZJWEVEOiBNYXRjaCBieSBleGFjdCBleHBpcnkgZGF0ZSBmcm9tIENTViBTTV9FWFBJUllfREFURSBjb2x1bW5cbiAgICBmb3IgKGNvbnN0IFtzZWN1cml0eUlkLCBkYXRhXSBvZiBBcnJheS5mcm9tKG1hcmtldERhdGEuZW50cmllcygpKSkge1xuICAgICAgLy8gQ2hlY2sgaWYgdGhpcyBpcyBhIE5JRlRZIG9wdGlvbiB3aXRoIG1hdGNoaW5nIGNyaXRlcmlhXG4gICAgICBpZiAoZGF0YS5zeW1ib2wuaW5jbHVkZXMoJ05JRlRZLScpICYmICAvLyBFeGFjdCBOSUZUWSBvcHRpb25zIChub3QgQkFOS05JRlRZLCBldGMuKVxuICAgICAgICAgIGRhdGEuc3ltYm9sLmluY2x1ZGVzKGAtJHtzdHJpa2V9LSR7b3B0aW9uVHlwZX1gKSAmJlxuICAgICAgICAgIGRhdGEuZXhwaXJ5RGF0ZSA9PT0gc2VsZWN0ZWRFeHBpcnkpIHsgLy8g4pyFIERpcmVjdCBleHBpcnkgZGF0ZSBtYXRjaCBmcm9tIENTViBTTV9FWFBJUllfREFURVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGBbT1BUSU9OXSDinIUgRm91bmQgJHtvcHRpb25UeXBlfSAke3N0cmlrZX06ICR7ZGF0YS5zeW1ib2x9IChFeHBpcnk6ICR7ZGF0YS5leHBpcnlEYXRlfSlgKTtcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHNlY3VyaXR5SWQsXG4gICAgICAgICAgc3ltYm9sOiBkYXRhLnN5bWJvbCxcbiAgICAgICAgICBleGNoYW5nZTogZGF0YS5leGNoYW5nZSxcbiAgICAgICAgICBzdHJpa2VQcmljZTogc3RyaWtlLFxuICAgICAgICAgIG9wdGlvblR5cGUsXG4gICAgICAgICAgZXhwaXJ5RGF0ZTogc2VsZWN0ZWRFeHBpcnksXG4gICAgICAgICAgbHRwOiBkYXRhLmx0cCB8fCAwLFxuICAgICAgICAgIGNoYW5nZTogZGF0YS5jaGFuZ2UgfHwgMCxcbiAgICAgICAgICBjaGFuZ2VQZXJjZW50OiBkYXRhLmNoYW5nZVBlcmNlbnQgfHwgMCxcbiAgICAgICAgICB2b2x1bWU6IGRhdGEudm9sdW1lIHx8IDAsXG4gICAgICAgICAgb3BlbkludGVyZXN0OiBkYXRhLm9wZW5JbnRlcmVzdCxcbiAgICAgICAgICBiaWQ6IGRhdGEubWFya2V0RGVwdGg/LlswXT8uYmlkUHJpY2UgfHwgZGF0YS5iaWQsXG4gICAgICAgICAgYXNrOiBkYXRhLm1hcmtldERlcHRoPy5bMF0/LmFza1ByaWNlIHx8IGRhdGEuYXNrLFxuICAgICAgICAgIGJpZFF0eTogZGF0YS5tYXJrZXREZXB0aD8uWzBdPy5iaWRRdHkgfHwgZGF0YS5iaWRRdHksXG4gICAgICAgICAgYXNrUXR5OiBkYXRhLm1hcmtldERlcHRoPy5bMF0/LmFza1F0eSB8fCBkYXRhLmFza1F0eSxcbiAgICAgICAgICBoaWdoOiBkYXRhLmhpZ2gsXG4gICAgICAgICAgbG93OiBkYXRhLmxvdyxcbiAgICAgICAgICBvcGVuOiBkYXRhLm9wZW4sXG4gICAgICAgICAgY2xvc2U6IGRhdGEuY2xvc2UsXG4gICAgICAgICAgdGltZXN0YW1wOiBkYXRhLnRpbWVzdGFtcFxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIERlYnVnOiBMb2cgd2hlbiBvcHRpb24gbm90IGZvdW5kXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICBjb25zb2xlLmxvZyhgW09QVElPTl0g4p2MIE5vdCBmb3VuZCAke29wdGlvblR5cGV9ICR7c3RyaWtlfSBmb3IgZXhwaXJ5ICR7c2VsZWN0ZWRFeHBpcnl9YCk7XG5cbiAgICAgIC8vIFNob3cgYXZhaWxhYmxlIG9wdGlvbnMgZm9yIGRlYnVnZ2luZ1xuICAgICAgY29uc3QgYXZhaWxhYmxlT3B0aW9ucyA9IEFycmF5LmZyb20obWFya2V0RGF0YS52YWx1ZXMoKSlcbiAgICAgICAgLmZpbHRlcihkYXRhID0+IGRhdGEuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWS0nKSAmJiBkYXRhLnN5bWJvbC5pbmNsdWRlcyhgLSR7b3B0aW9uVHlwZX1gKSlcbiAgICAgICAgLnNsaWNlKDAsIDUpO1xuXG4gICAgICBjb25zb2xlLmxvZyhgW0RFQlVHXSBBdmFpbGFibGUgJHtvcHRpb25UeXBlfSBvcHRpb25zOmAsIGF2YWlsYWJsZU9wdGlvbnMubWFwKG9wdCA9PlxuICAgICAgICBgJHtvcHQuc3ltYm9sfSAoRXhwaXJ5OiAke29wdC5leHBpcnlEYXRlfSlgXG4gICAgICApKTtcbiAgICB9XG5cbiAgICByZXR1cm4gbnVsbDtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRFeHBpcnlGb3JTeW1ib2wgPSAoZXhwaXJ5OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIC8vIENvbnZlcnQgWVlZWS1NTS1ERCB0byBmb3JtYXQgdXNlZCBpbiBzeW1ib2xzIChlLmcuLCBKdW4yMDI1KVxuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShleHBpcnkpO1xuICAgIGNvbnN0IG1vbnRocyA9IFsnSmFuJywgJ0ZlYicsICdNYXInLCAnQXByJywgJ01heScsICdKdW4nLFxuICAgICAgICAgICAgICAgICAgICdKdWwnLCAnQXVnJywgJ1NlcCcsICdPY3QnLCAnTm92JywgJ0RlYyddO1xuICAgIHJldHVybiBgJHttb250aHNbZGF0ZS5nZXRNb250aCgpXX0ke2RhdGUuZ2V0RnVsbFllYXIoKX1gO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFByaWNlID0gKHByaWNlOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmICghcHJpY2UgfHwgcHJpY2UgPD0gMCkgcmV0dXJuICdOL0EnO1xuICAgIHJldHVybiBg4oK5JHtwcmljZS50b0ZpeGVkKDIpfWA7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0TnVtYmVyID0gKG51bTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIW51bSkgcmV0dXJuICdOL0EnO1xuICAgIHJldHVybiBudW0udG9Mb2NhbGVTdHJpbmcoKTtcbiAgfTtcblxuICBjb25zdCBnZXRDaGFuZ2VDb2xvciA9IChjaGFuZ2U6IG51bWJlciB8IHVuZGVmaW5lZCk6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFjaGFuZ2UpIHJldHVybiAndGV4dC1ncmF5LTQwMCc7XG4gICAgcmV0dXJuIGNoYW5nZSA+IDAgPyAndGV4dC1ncmVlbi00MDAnIDogY2hhbmdlIDwgMCA/ICd0ZXh0LXJlZC00MDAnIDogJ3RleHQtZ3JheS00MDAnO1xuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TG9hZGluZyBvcHRpb24gY2hhaW4uLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcmVkLTgwMCBmb250LW1lZGl1bVwiPkVycm9yIExvYWRpbmcgT3B0aW9uIENoYWluPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIHRleHQtc20gbXQtMVwiPntlcnJvcn08L3A+XG4gICAgICAgIDxidXR0b24gXG4gICAgICAgICAgb25DbGljaz17ZmV0Y2hFeHBpcnlEYXRlc31cbiAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHB4LTMgcHktMSBiZy1yZWQtNjAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkIGhvdmVyOmJnLXJlZC03MDBcIlxuICAgICAgICA+XG4gICAgICAgICAgUmV0cnlcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIG1pbi1oLXNjcmVlblwiPlxuICAgICAgey8qIEhlYWRlciBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcHgtNiBweS00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTZcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPk9wdGlvbnM8L2gxPlxuXG4gICAgICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMCBiZy1ibHVlLTUwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICDwn5SNIE5JRlRZXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgICAgU3RyYXRlZ3kgYnVpbGRlclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICAgIENsYXNzXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICAgICAgVm9sYXRpbGl0eVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFJpZ2h0IHNpZGUgY29udHJvbHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIEJ5IGV4cGlyYXRpb24gfCBieSBzdHJpa2VcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+TklGVFkgU3BvdDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2UobmlmdHlTcG90UHJpY2UpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIHtuaWZ0eVNwb3RQcmljZSA9PT0gMjQ4NTAgJiYgKFxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPihNb2NrKTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRXhwaXJ5IFNlbGVjdGlvbiAtIEVuaGFuY2VkIEZvcm1hdCAqL31cbiAgICAgIHtleHBpcnlEYXRhICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcHgtNiBweS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgb3ZlcmZsb3cteC1hdXRvIHBiLTJcIj5cbiAgICAgICAgICAgIHtleHBpcnlEYXRhLmV4cGlyaWVzLnNsaWNlKDAsIDE1KS5tYXAoKGV4cGlyeSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGV4cGlyeSk7XG4gICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBleHBpcnkgPT09IHNlbGVjdGVkRXhwaXJ5O1xuICAgICAgICAgICAgICBjb25zdCBpc1RvZGF5ID0gZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0gbmV3IERhdGUoKS50b0RhdGVTdHJpbmcoKTtcbiAgICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50V2VlayA9IE1hdGguYWJzKGRhdGUuZ2V0VGltZSgpIC0gbmV3IERhdGUoKS5nZXRUaW1lKCkpIDwgNyAqIDI0ICogNjAgKiA2MCAqIDEwMDA7XG5cbiAgICAgICAgICAgICAgLy8gRW5oYW5jZWQgZGF0ZSBmb3JtYXR0aW5nXG4gICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xuICAgICAgICAgICAgICBjb25zdCBleHBpcnlZZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpO1xuICAgICAgICAgICAgICBjb25zdCBtb250aFNob3J0ID0gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ3Nob3J0JyB9KTtcbiAgICAgICAgICAgICAgY29uc3QgZGF5ID0gZGF0ZS5nZXREYXRlKCk7XG5cbiAgICAgICAgICAgICAgLy8gU2hvdyB5ZWFyIGlmIGRpZmZlcmVudCBmcm9tIGN1cnJlbnQgeWVhclxuICAgICAgICAgICAgICBjb25zdCBzaG93WWVhciA9IGV4cGlyeVllYXIgIT09IGN1cnJlbnRZZWFyO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXtleHBpcnl9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZEV4cGlyeShleHBpcnkpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC1zaHJpbmstMCBweC0zIHB5LTIgdGV4dC14cyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBib3JkZXIgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNTZWxlY3RlZFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsYWNrIHRleHQtd2hpdGUgYm9yZGVyLWJsYWNrJ1xuICAgICAgICAgICAgICAgICAgICAgIDogaXNUb2RheVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLW9yYW5nZS01MDAgdGV4dC13aGl0ZSBib3JkZXItb3JhbmdlLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6IGlzQ3VycmVudFdlZWtcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIGJvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTIwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy13aGl0ZSB0ZXh0LWdyYXktNzAwIGJvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTUwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtaW4tdy1bNDBweF1cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbm9ybWFsIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bW9udGhTaG9ydH17c2hvd1llYXIgJiYgYCAke2V4cGlyeVllYXIudG9TdHJpbmcoKS5zbGljZSgtMil9YH1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZGF5fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRW5oYW5jZWQgRGVidWcgSW5mbyAqL31cbiAgICAgICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIHRleHQteHMgdGV4dC1ncmF5LTUwMCBiZy1ncmF5LTEwMCBwLTMgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PjxzdHJvbmc+U2VsZWN0ZWQgRXhwaXJ5Ojwvc3Ryb25nPiB7c2VsZWN0ZWRFeHBpcnl9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPk5JRlRZIFNwb3Q6PC9zdHJvbmc+IOKCuXtuaWZ0eVNwb3RQcmljZX0ge25pZnR5U3BvdFByaWNlID09PSAyNDg1MCA/ICcoTW9jayknIDogJyhMaXZlKSd9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPkF2YWlsYWJsZSBTdHJpa2VzOjwvc3Ryb25nPiB7b3B0aW9uQ2hhaW4/LnJvd3MubGVuZ3RoIHx8IDB9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPk1hcmtldCBEYXRhIFNpemU6PC9zdHJvbmc+IHttYXJrZXREYXRhLnNpemV9IGluc3RydW1lbnRzPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48c3Ryb25nPk5JRlRZIE9wdGlvbnM6PC9zdHJvbmc+IHtBcnJheS5mcm9tKG1hcmtldERhdGEudmFsdWVzKCkpLmZpbHRlcihkID0+IGQuc3ltYm9sLmluY2x1ZGVzKCdOSUZUWS0nKSkubGVuZ3RofSBmb3VuZDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PHN0cm9uZz5PcHRpb25zIHdpdGggRXhwaXJ5Ojwvc3Ryb25nPiB7QXJyYXkuZnJvbShtYXJrZXREYXRhLnZhbHVlcygpKS5maWx0ZXIoZCA9PiBkLnN5bWJvbC5pbmNsdWRlcygnTklGVFktJykgJiYgZC5leHBpcnlEYXRlKS5sZW5ndGh9IG1hdGNoZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBPcHRpb24gQ2hhaW4gVGFibGUgKi99XG4gICAgICB7b3B0aW9uQ2hhaW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlXCI+XG4gICAgICAgICAgey8qIFRhYmxlIEhlYWRlcnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJnLWdyYXktNTAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgIHsvKiBDYWxscyBIZWFkZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBmb250LWJvbGRcIj5DYWxsczwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgey8qIFN0cmlrZSBIZWFkZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgdGV4dC1jZW50ZXIgcHktMyBib3JkZXItciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1ib2xkXCI+U3RyaWtlPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7LyogUHV0cyBIZWFkZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBmb250LWJvbGRcIj5QdXRzPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ29sdW1uIEhlYWRlcnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJnLWdyYXktMTAwIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICB7LyogQ2FsbCBjb2x1bW5zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+T0k8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPlZvbHVtZTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+QmlkPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5Bc2s8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkNoYW5nZTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+TFRQPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTEgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwXCI+U2VjdXJpdHkgSUQ8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFN0cmlrZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCB0ZXh0LWNlbnRlciBweS0yIHB4LTEgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwIGZvbnQtYm9sZFwiPlN0cmlrZTwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUHV0IGNvbHVtbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5TZWN1cml0eSBJRDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+TFRQPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5DaGFuZ2U8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPkFzazwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMiBweC0xXCI+QmlkPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0yIHB4LTFcIj5Wb2x1bWU8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTIgcHgtMVwiPk9JPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVGFibGUgQm9keSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS1ncmF5LTEwMFwiPlxuICAgICAgICAgICAge29wdGlvbkNoYWluLnJvd3MubWFwKChyb3csIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGlzQVRNID0gTWF0aC5hYnMocm93LnN0cmlrZVByaWNlIC0gbmlmdHlTcG90UHJpY2UpIDw9IDUwO1xuICAgICAgICAgICAgICBjb25zdCBpc0lUTV9DYWxsID0gcm93LnN0cmlrZVByaWNlIDwgbmlmdHlTcG90UHJpY2U7XG4gICAgICAgICAgICAgIGNvbnN0IGlzSVRNX1B1dCA9IHJvdy5zdHJpa2VQcmljZSA+IG5pZnR5U3BvdFByaWNlO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtyb3cuc3RyaWtlUHJpY2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNBVE0gPyAnYmcteWVsbG93LTUwJyA6IGluZGV4ICUgMiA9PT0gMCA/ICdiZy13aGl0ZScgOiAnYmctZ3JheS0yNSdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHsvKiBDYWxsIE9wdGlvbnMgRGF0YSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtICR7aXNJVE1fQ2FsbCA/ICd0ZXh0LWdyZWVuLTcwMCBmb250LW1lZGl1bScgOiAndGV4dC1ncmF5LTcwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmNhbGw/Lm9wZW5JbnRlcmVzdCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtICR7aXNJVE1fQ2FsbCA/ICd0ZXh0LWdyZWVuLTcwMCBmb250LW1lZGl1bScgOiAndGV4dC1ncmF5LTcwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmNhbGw/LnZvbHVtZSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cuY2FsbD8uYmlkKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cuY2FsbD8uYXNrKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gZm9udC1tZWRpdW0gJHtnZXRDaGFuZ2VDb2xvcihyb3cuY2FsbD8uY2hhbmdlKX1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Jvdy5jYWxsPy5jaGFuZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyb3cuY2FsbC5jaGFuZ2UgPiAwID8gJysnIDogJyd9e3Jvdy5jYWxsLmNoYW5nZS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICh7cm93LmNhbGwuY2hhbmdlUGVyY2VudCA+IDAgPyAnKycgOiAnJ317cm93LmNhbGwuY2hhbmdlUGVyY2VudC50b0ZpeGVkKDEpfSUpXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgKSA6ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LWJvbGQgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwICR7XG4gICAgICAgICAgICAgICAgICAgIGlzSVRNX0NhbGwgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtZ3JheS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cuY2FsbD8ubHRwKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogU3RyaWtlIFByaWNlICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIwIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIGZvbnQtYm9sZCBib3JkZXItciBib3JkZXItZ3JheS0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNBVE0gPyAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnIDogJ3RleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtyb3cuc3RyaWtlUHJpY2V9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFB1dCBPcHRpb25zIERhdGEgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSBmb250LWJvbGQgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNJVE1fUHV0ID8gJ3RleHQtcmVkLTYwMCcgOiAndGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHJvdy5wdXQ/Lmx0cCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC0xIHRleHQtY2VudGVyIHB5LTMgcHgtMSB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3Iocm93LnB1dD8uY2hhbmdlKX1gfT5cbiAgICAgICAgICAgICAgICAgICAge3Jvdy5wdXQ/LmNoYW5nZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Jvdy5wdXQuY2hhbmdlID4gMCA/ICcrJyA6ICcnfXtyb3cucHV0LmNoYW5nZS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICh7cm93LnB1dC5jaGFuZ2VQZXJjZW50ID4gMCA/ICcrJyA6ICcnfXtyb3cucHV0LmNoYW5nZVBlcmNlbnQudG9GaXhlZCgxKX0lKVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAnTi9BJ31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShyb3cucHV0Py5hc2spfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2Uocm93LnB1dD8uYmlkKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LTEgdGV4dC1jZW50ZXIgcHktMyBweC0xIHRleHQtc20gJHtpc0lUTV9QdXQgPyAndGV4dC1yZWQtNzAwIGZvbnQtbWVkaXVtJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cucHV0Py52b2x1bWUpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtMSB0ZXh0LWNlbnRlciBweS0zIHB4LTEgdGV4dC1zbSAke2lzSVRNX1B1dCA/ICd0ZXh0LXJlZC03MDAgZm9udC1tZWRpdW0nIDogJ3RleHQtZ3JheS03MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TnVtYmVyKHJvdy5wdXQ/Lm9wZW5JbnRlcmVzdCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICB7b3B0aW9uQ2hhaW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4+U2hvdyBhbGw8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPuKAojwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+U2hvd2luZyB7b3B0aW9uQ2hhaW4ucm93cy5sZW5ndGh9IHN0cmlrZXMgYXJvdW5kIEFUTTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgTGFzdCB1cGRhdGVkOiB7bmV3IERhdGUob3B0aW9uQ2hhaW4udGltZXN0YW1wKS50b0xvY2FsZVRpbWVTdHJpbmcoKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJPcHRpb25DaGFpbiIsIm1hcmtldERhdGEiLCJleHBpcnlEYXRhIiwic2V0RXhwaXJ5RGF0YSIsInNlbGVjdGVkRXhwaXJ5Iiwic2V0U2VsZWN0ZWRFeHBpcnkiLCJvcHRpb25DaGFpbiIsInNldE9wdGlvbkNoYWluIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibmlmdHlTcG90UHJpY2UiLCJzZXROaWZ0eVNwb3RQcmljZSIsIm5pZnR5RGF0YSIsImdldCIsImx0cCIsImNvbnNvbGUiLCJsb2ciLCJzZWN1cml0eUlkIiwiZGF0YSIsIkFycmF5IiwiZnJvbSIsImVudHJpZXMiLCJzeW1ib2wiLCJleGNoYW5nZSIsIm1vY2tQcmljZSIsIndhcm4iLCJmZXRjaEV4cGlyeURhdGVzIiwiYnVpbGRPcHRpb25DaGFpbiIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwic3RhdHVzVGV4dCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiZXhwaXJpZXMiLCJsZW5ndGgiLCJtZXNzYWdlIiwiZXJyIiwic3RyaWtlcyIsImdlbmVyYXRlU3RyaWtlcyIsInJvd3MiLCJtYXAiLCJzdHJpa2UiLCJjYWxsRGF0YSIsImZpbmRPcHRpb25EYXRhIiwicHV0RGF0YSIsInN0cmlrZVByaWNlIiwiY2FsbCIsInB1dCIsInVuZGVybHlpbmciLCJzcG90UHJpY2UiLCJleHBpcnkiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwiYXZhaWxhYmxlU3RyaWtlcyIsIlNldCIsImluY2x1ZGVzIiwiZm9ybWF0RXhwaXJ5Rm9yU3ltYm9sIiwic3ltYm9sUGFydHMiLCJzcGxpdCIsInBhcnNlRmxvYXQiLCJpc05hTiIsImFkZCIsInNpemUiLCJiYXNlU3RyaWtlIiwiTWF0aCIsInJvdW5kIiwiaSIsInB1c2giLCJzb3J0IiwiYSIsImIiLCJhbGxTdHJpa2VzIiwiYXRtU3RyaWtlIiwicmVkdWNlIiwicHJldiIsImN1cnIiLCJhYnMiLCJhdG1JbmRleCIsImluZGV4T2YiLCJzZWxlY3RlZFN0cmlrZXMiLCJzbGljZSIsIm1heCIsIm9wdGlvblR5cGUiLCJleHBpcnlEYXRlIiwiY2hhbmdlIiwiY2hhbmdlUGVyY2VudCIsInZvbHVtZSIsIm9wZW5JbnRlcmVzdCIsImJpZCIsIm1hcmtldERlcHRoIiwiYmlkUHJpY2UiLCJhc2siLCJhc2tQcmljZSIsImJpZFF0eSIsImFza1F0eSIsImhpZ2giLCJsb3ciLCJvcGVuIiwiY2xvc2UiLCJwcm9jZXNzIiwiYXZhaWxhYmxlT3B0aW9ucyIsInZhbHVlcyIsImZpbHRlciIsIm9wdCIsImRhdGUiLCJtb250aHMiLCJnZXRNb250aCIsImdldEZ1bGxZZWFyIiwiZm9ybWF0UHJpY2UiLCJwcmljZSIsInRvRml4ZWQiLCJmb3JtYXROdW1iZXIiLCJudW0iLCJ0b0xvY2FsZVN0cmluZyIsImdldENoYW5nZUNvbG9yIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImgzIiwiYnV0dG9uIiwib25DbGljayIsImgxIiwic3BhbiIsImluZGV4IiwiaXNTZWxlY3RlZCIsImlzVG9kYXkiLCJ0b0RhdGVTdHJpbmciLCJpc0N1cnJlbnRXZWVrIiwiZ2V0VGltZSIsImN1cnJlbnRZZWFyIiwiZXhwaXJ5WWVhciIsIm1vbnRoU2hvcnQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJtb250aCIsImRheSIsImdldERhdGUiLCJzaG93WWVhciIsInRvU3RyaW5nIiwic3Ryb25nIiwiZCIsInJvdyIsImlzQVRNIiwiaXNJVE1fQ2FsbCIsImlzSVRNX1B1dCIsInRvTG9jYWxlVGltZVN0cmluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptionChain.tsx\n"));

/***/ })

});