// <PERSON>ript to inspect the database structure and data
const { Client } = require('pg');
const fs = require('fs');

// Manually load .env file
const envContent = fs.readFileSync('.env', 'utf8');
console.log('📄 .env file content preview:', envContent.substring(0, 200));

envContent.split('\n').forEach((line, index) => {
  console.log(`Line ${index + 1}: "${line}" (length: ${line.length})`);
  if (line.trim() && !line.startsWith('#')) {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      const value = valueParts.join('='); // Handle URLs with = in them
      process.env[key.trim()] = value.trim();
      console.log(`  → Parsed: ${key.trim()} = ${value.trim().substring(0, 50)}...`);
      if (key.trim() === 'DATABASE_URL') {
        console.log('🔗 Found DATABASE_URL in .env');
      }
    }
  }
});

async function inspectDatabase() {
  console.log('🔗 Database URL:', process.env.DATABASE_URL ? 'Found' : 'Not found');
  console.log('🔗 Full URL:', process.env.DATABASE_URL);

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false // For Supabase/cloud databases
    }
  });

  try {
    console.log('🔍 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database successfully!');

    // 1. Check if Instruments table exists
    console.log('\n📋 Checking table existence...');
    const tableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name ILIKE '%instrument%'
    `);
    
    console.log('Tables found:', tableCheck.rows.map(row => row.table_name));

    if (tableCheck.rows.length === 0) {
      console.log('❌ No Instruments table found. Let me check all tables:');
      const allTables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      console.log('All tables:', allTables.rows.map(row => row.table_name));
      return;
    }

    const tableName = tableCheck.rows[0].table_name;
    console.log(`✅ Found table: ${tableName}`);

    // 2. Get table structure
    console.log(`\n🏗️ Getting structure of table '${tableName}'...`);
    const structure = await client.query(`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);

    console.log('\n📊 Table Structure:');
    structure.rows.forEach((col, index) => {
      console.log(`${index + 1}. ${col.column_name} (${col.data_type}${col.character_maximum_length ? `(${col.character_maximum_length})` : ''}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // 3. Get row count
    console.log(`\n📈 Getting row count...`);
    const countResult = await client.query(`SELECT COUNT(*) as total FROM "${tableName}"`);
    const totalRows = countResult.rows[0].total;
    console.log(`Total rows: ${totalRows}`);

    // 4. Get sample data
    console.log(`\n📋 Sample data (first 5 rows):`);
    const sampleData = await client.query(`SELECT * FROM "${tableName}" LIMIT 5`);
    
    if (sampleData.rows.length > 0) {
      console.log('\nColumns:', Object.keys(sampleData.rows[0]).join(', '));
      sampleData.rows.forEach((row, index) => {
        console.log(`\nRow ${index + 1}:`);
        Object.entries(row).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`);
        });
      });
    }

    // 5. Check for NIFTY options specifically
    console.log(`\n🎯 Checking for NIFTY options...`);
    const niftyCheck = await client.query(`
      SELECT COUNT(*) as count 
      FROM "${tableName}" 
      WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
      AND "OPTION_TYPE" IN ('CE', 'PE')
    `);
    
    console.log(`NIFTY options found: ${niftyCheck.rows[0].count}`);

    if (niftyCheck.rows[0].count > 0) {
      // Get sample NIFTY options
      console.log(`\n📊 Sample NIFTY options:`);
      const niftySample = await client.query(`
        SELECT 
          "SECURITY_ID",
          "SYMBOL_NAME", 
          "SM_EXPIRY_DATE",
          "STRIKE_PRICE",
          "OPTION_TYPE",
          "UNDERLYING_SYMBOL"
        FROM "${tableName}" 
        WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
        AND "OPTION_TYPE" IN ('CE', 'PE')
        ORDER BY "SM_EXPIRY_DATE", "STRIKE_PRICE"
        LIMIT 10
      `);

      niftySample.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.SYMBOL_NAME} (ID: ${row.SECURITY_ID})`);
        console.log(`   Expiry: ${row.SM_EXPIRY_DATE}, Strike: ${row.STRIKE_PRICE}, Type: ${row.OPTION_TYPE}`);
      });

      // Get available expiry dates
      console.log(`\n📅 Available NIFTY expiry dates:`);
      const expiries = await client.query(`
        SELECT DISTINCT "SM_EXPIRY_DATE"
        FROM "${tableName}" 
        WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
        AND "OPTION_TYPE" IN ('CE', 'PE')
        AND "SM_EXPIRY_DATE" IS NOT NULL
        ORDER BY "SM_EXPIRY_DATE"
        LIMIT 10
      `);

      expiries.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.SM_EXPIRY_DATE}`);
      });

      // Get strikes around current price
      console.log(`\n🎯 NIFTY options around 24850 strike:`);
      const nearbyStrikes = await client.query(`
        SELECT 
          "SECURITY_ID",
          "SYMBOL_NAME", 
          "SM_EXPIRY_DATE",
          "STRIKE_PRICE",
          "OPTION_TYPE"
        FROM "${tableName}" 
        WHERE "SYMBOL_NAME" ILIKE 'NIFTY-%' 
        AND "OPTION_TYPE" IN ('CE', 'PE')
        AND "STRIKE_PRICE"::numeric BETWEEN 24650 AND 25050
        ORDER BY "SM_EXPIRY_DATE", "STRIKE_PRICE"::numeric, "OPTION_TYPE"
        LIMIT 20
      `);

      nearbyStrikes.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.SYMBOL_NAME} (ID: ${row.SECURITY_ID})`);
        console.log(`   Expiry: ${row.SM_EXPIRY_DATE}, Strike: ${row.STRIKE_PRICE}, Type: ${row.OPTION_TYPE}`);
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await client.end();
    console.log('\n🔌 Database connection closed');
  }
}

inspectDatabase();
