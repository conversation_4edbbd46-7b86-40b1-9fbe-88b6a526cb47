// Test the date formatting issue
console.log('🧪 Testing date format conversion...');

const formatExpiryForSymbol = (expiry) => {
  // Convert YYYY-MM-DD to format used in symbols (e.g., Jun2025)
  const date = new Date(expiry);
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return `${months[date.getMonth()]}${date.getFullYear()}`;
};

// Test with API expiry dates
const apiExpiries = [
  '2025-07-10', '2025-07-17', '2025-07-24', '2025-07-31',
  '2025-08-07', '2025-08-28', '2025-09-25', '2025-12-24'
];

// Test with CSV expiry dates  
const csvExpiries = [
  '2025-06-19', '2025-06-26', '2025-07-03', '2025-07-10',
  '2025-07-17', '2025-08-28'
];

console.log('\n📅 API Expiry Date Formatting:');
apiExpiries.forEach(expiry => {
  const formatted = formatExpiryForSymbol(expiry);
  console.log(`   ${expiry} → ${formatted}`);
});

console.log('\n📋 CSV Expiry Date Formatting:');
csvExpiries.forEach(expiry => {
  const formatted = formatExpiryForSymbol(expiry);
  console.log(`   ${expiry} → ${formatted}`);
});

// Test with sample NIFTY symbols from CSV
const sampleSymbols = [
  'NIFTY-Jun2025-18250-CE',
  'NIFTY-Jun2025-24850-CE', 
  'NIFTY-Jul2025-24650-CE',
  'NIFTY-Aug2025-19500-CE'
];

console.log('\n🎯 Sample NIFTY symbols from CSV:');
sampleSymbols.forEach(symbol => {
  const parts = symbol.split('-');
  if (parts.length >= 2) {
    const symbolExpiry = parts[1]; // e.g., "Jun2025"
    console.log(`   ${symbol} → Expiry in symbol: ${symbolExpiry}`);
    
    // Check which CSV expiry would match this
    csvExpiries.forEach(csvExpiry => {
      const formatted = formatExpiryForSymbol(csvExpiry);
      if (formatted === symbolExpiry) {
        console.log(`     ✅ Matches CSV expiry: ${csvExpiry}`);
      }
    });
    
    // Check which API expiry would match this
    apiExpiries.forEach(apiExpiry => {
      const formatted = formatExpiryForSymbol(apiExpiry);
      if (formatted === symbolExpiry) {
        console.log(`     🔍 Matches API expiry: ${apiExpiry}`);
      }
    });
  }
});

console.log('\n🔍 Testing specific date conversion:');
console.log('2025-06-26 →', formatExpiryForSymbol('2025-06-26'));
console.log('2025-07-10 →', formatExpiryForSymbol('2025-07-10'));
console.log('2025-07-31 →', formatExpiryForSymbol('2025-07-31'));
