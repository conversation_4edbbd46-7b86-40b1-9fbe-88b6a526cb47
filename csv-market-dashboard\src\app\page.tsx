"use client";

import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { Instrument, MarketData, InstrumentFilter } from '@/types';
import InstrumentTable from '@/components/InstrumentTable';
import FilterPanel from '@/components/FilterPanel';
import ConnectionStatus from '@/components/ConnectionStatus';
import Stats from '@/components/Stats';

export default function Dashboard() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(new Map());
  const [filter, setFilter] = useState<InstrumentFilter>({});
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exchanges, setExchanges] = useState<string[]>([]);
  const [instrumentTypes, setInstrumentTypes] = useState<string[]>([]);

  // Initialize socket connection
  useEffect(() => {
    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8080';
    const socketInstance = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
    });

    socketInstance.on('connect', () => {
      console.log('✅ Connected to server');
      setConnected(true);
      setError(null);
    });

    socketInstance.on('disconnect', () => {
      console.log('❌ Disconnected from server');
      setConnected(false);
    });

    socketInstance.on('initialData', (data: {
      instruments: Instrument[];
      marketData: MarketData[];
      connected: boolean;
      totalInstruments: number;
    }) => {
      console.log('📊 Received initial connection data, total instruments:', data.totalInstruments);

      const marketDataMap = new Map<string, MarketData>();
      data.marketData.forEach(md => {
        marketDataMap.set(md.securityId, md);
      });
      setMarketData(marketDataMap);

      // Load all instruments from API instead of socket
      loadAllInstruments();
    });

    socketInstance.on('marketData', (data: MarketData) => {
      setMarketData(prev => {
        const newMap = new Map(prev);
        newMap.set(data.securityId, data);
        return newMap;
      });
    });

    socketInstance.on('connectionStatus', (data: { connected: boolean }) => {
      setConnected(data.connected);
    });

    socketInstance.on('error', (data: { message: string; error?: string }) => {
      console.error('❌ Socket error:', data);
      setError(data.message);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  // Load all instruments from API
  const loadAllInstruments = async () => {
    try {
      const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8080';
      console.log('📊 Loading all instruments from API...');

      const response = await fetch(`${serverUrl}/api/instruments`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded', data.data.instruments.length, 'instruments from API');
        setInstruments(data.data.instruments);
        setLoading(false);
      } else {
        console.error('❌ Failed to load instruments:', response.statusText);
        setError('Failed to load instruments');
        setLoading(false);
      }
    } catch (error) {
      console.error('❌ Error loading instruments:', error);
      setError('Error loading instruments');
      setLoading(false);
    }
  };

  // Load additional data
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8080';
        
        // Load exchanges
        const exchangesResponse = await fetch(`${serverUrl}/api/exchanges`);
        if (exchangesResponse.ok) {
          const exchangesData = await exchangesResponse.json();
          setExchanges(exchangesData.data);
        }

        // Load instrument types
        const typesResponse = await fetch(`${serverUrl}/api/instrument-types`);
        if (typesResponse.ok) {
          const typesData = await typesResponse.json();
          setInstrumentTypes(typesData.data);
        }
      } catch (error) {
        console.error('❌ Error loading metadata:', error);
      }
    };

    loadMetadata();
  }, []);

  // Filter instruments based on current filter
  const filteredInstruments = instruments.filter(instrument => {
    if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {
      return false;
    }
    if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {
      return false;
    }
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      return (
        instrument.symbol.toLowerCase().includes(searchTerm) ||
        instrument.displayName.toLowerCase().includes(searchTerm) ||
        (instrument.isin && instrument.isin.toLowerCase().includes(searchTerm))
      );
    }
    return true;
  });

  // Subscribe to instruments when filter changes
  useEffect(() => {
    if (socket && filteredInstruments.length > 0) {
      const securityIds = filteredInstruments.slice(0, 100).map(inst => inst.securityId); // Limit to 100 for performance
      socket.emit('subscribe', { securityIds });
    }
  }, [socket, filteredInstruments]);

  const handleFilterChange = (newFilter: InstrumentFilter) => {
    setFilter(newFilter);
  };

  const handleInstrumentSelect = (instrument: Instrument) => {
    console.log('Selected instrument:', instrument);
    // You can add more functionality here, like showing detailed view
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading market data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="glass rounded-2xl shadow-lg p-6 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold gradient-text">CSV Market Dashboard</h1>
            <p className="text-gray-600 mt-1">Real-time market data from CSV instruments</p>
            <div className="mt-3 space-x-3">
              <a
                href="/subscribed"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
              >
                📊 View Subscribed Data Dashboard
              </a>
              <a
                href="/option-chain"
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
              >
                🔗 NIFTY Option Chain
              </a>
            </div>
          </div>
          <ConnectionStatus connected={connected} />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Stats */}
      <Stats 
        totalInstruments={instruments.length}
        filteredInstruments={filteredInstruments.length}
        marketDataCount={marketData.size}
        connected={connected}
      />

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filter Panel */}
        <div className="lg:col-span-1">
          <FilterPanel
            filter={filter}
            onFilterChange={handleFilterChange}
            exchanges={exchanges}
            instrumentTypes={instrumentTypes}
            segments={['C', 'F', 'O']} // Common segments
          />
        </div>

        {/* Instrument Table */}
        <div className="lg:col-span-3">
          <InstrumentTable
            instruments={filteredInstruments.slice(0, 100)} // Show more instruments
            marketData={marketData}
            onInstrumentSelect={handleInstrumentSelect}
            loading={loading}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>CSV Market Dashboard - Real-time data from {instruments.length} instruments</p>
        <p>Last updated: {new Date().toLocaleTimeString()}</p>
      </div>
    </div>
  );
}
