{"name": "csv-market-dashboard", "version": "0.1.0", "private": true, "description": "Real-time market data dashboard using CSV instrument subscription", "scripts": {"dev": "concurrently \"next dev\" \"npm run dev:server\"", "dev:next": "next dev", "dev:server": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register --project tsconfig.server.json src/server/main.ts", "build": "npm run clean && npm run build:next && npm run build:server", "build:next": "next build", "build:server": "tsc --project tsconfig.server.json && tsc-alias -p tsconfig.server.json", "start": "concurrently \"npm run start:next\" \"npm run start:server\"", "start:server": "node dist/server/main.js", "start:next": "next start", "lint": "next lint && tsc --noEmit --project tsconfig.server.json", "clean": "rimraf dist .next", "type-check": "tsc --noEmit && tsc --noEmit --project tsconfig.server.json", "csv:parse": "ts-node scripts/parse-csv.ts", "csv:test": "ts-node scripts/test-csv-instruments.ts"}, "dependencies": {"@types/pg": "^8.15.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "framer-motion": "^12.15.0", "helmet": "^7.2.0", "lucide-react": "^0.263.1", "multer": "^1.4.5-lts.2", "next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "recharts": "^2.15.3", "sharp": "^0.33.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.2", "zod": "^3.25.56"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/helmet": "^4.0.0", "@types/multer": "^1.4.13", "@types/node": "^20.19.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-next": "^14.2.28", "kill-port": "^2.0.1", "postcss": "^8.5.4", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "rimraf": "^5.0.5", "tailwindcss": "^3.4.17", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "engines": {"node": "22.x", "pnpm": ">=9.0.0"}}