// Debug script to find exact NIFTY options that should be available
const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 Finding exact NIFTY options for debugging...');

const niftyOptions = [];
let count = 0;

fs.createReadStream('./instruments.csv')
  .pipe(csv())
  .on('data', (data) => {
    count++;
    
    // Look for NIFTY options specifically (not BANKNIFTY, FINNIFTY, etc.)
    if (data.SYMBOL_NAME && 
        data.SYMBOL_NAME.startsWith('NIFTY-') &&  // Exact NIFTY, not BANKNIFTY
        data.INSTRUMENT === 'OPTIDX' && 
        ['CE', 'PE'].includes(data.OPTION_TYPE) &&
        data.UNDERLYING_SYMBOL === 'NIFTY') {  // Make sure underlying is NIFTY
      
      niftyOptions.push({
        SECURITY_ID: data.SECURITY_ID,
        SYMBOL_NAME: data.SYMBOL_NAME,
        SM_EXPIRY_DATE: data.SM_EXPIRY_DATE,
        STRIKE_PRICE: data.STRIKE_PRICE,
        OPTION_TYPE: data.OPTION_TYPE,
        INSTRUMENT: data.INSTRUMENT,
        UNDERLYING_SYMBOL: data.UNDERLYING_SYMBOL,
        EXCH_ID: data.EXCH_ID,
        SEGMENT: data.SEGMENT
      });
    }
  })
  .on('end', () => {
    console.log(`\n✅ Total rows processed: ${count}`);
    console.log(`🎯 Pure NIFTY options found: ${niftyOptions.length}`);
    
    if (niftyOptions.length > 0) {
      console.log('\n📊 First 10 NIFTY options:');
      niftyOptions.slice(0, 10).forEach((option, index) => {
        console.log(`${index + 1}. ${option.SYMBOL_NAME}`);
        console.log(`   Security ID: ${option.SECURITY_ID}`);
        console.log(`   Expiry: ${option.SM_EXPIRY_DATE}`);
        console.log(`   Strike: ${option.STRIKE_PRICE}`);
        console.log(`   Type: ${option.OPTION_TYPE}`);
        console.log(`   Exchange: ${option.EXCH_ID}/${option.SEGMENT}`);
        console.log('');
      });
      
      // Group by expiry date
      const expiryGroups = {};
      niftyOptions.forEach(option => {
        const expiry = option.SM_EXPIRY_DATE;
        if (!expiryGroups[expiry]) {
          expiryGroups[expiry] = [];
        }
        expiryGroups[expiry].push(option);
      });
      
      console.log('\n📅 NIFTY options by expiry date:');
      const sortedExpiries = Object.keys(expiryGroups).sort();
      sortedExpiries.slice(0, 5).forEach(expiry => {
        const options = expiryGroups[expiry];
        console.log(`   ${expiry}: ${options.length} options`);
        
        // Show sample strikes for this expiry
        const strikes = [...new Set(options.map(o => parseFloat(o.STRIKE_PRICE)))].sort((a, b) => a - b);
        console.log(`     Strikes: ${strikes.slice(0, 10).join(', ')}${strikes.length > 10 ? '...' : ''}`);
        
        // Show sample security IDs
        const sampleOptions = options.slice(0, 4);
        console.log(`     Sample IDs: ${sampleOptions.map(o => `${o.SECURITY_ID}(${o.OPTION_TYPE})`).join(', ')}`);
      });
      
      // Find options around current NIFTY price (24850)
      const currentPrice = 24850;
      const nearbyOptions = niftyOptions.filter(option => {
        const strike = parseFloat(option.STRIKE_PRICE);
        return Math.abs(strike - currentPrice) <= 200; // Within 200 points
      });
      
      console.log(`\n🎯 Options near current price (${currentPrice} ± 200):`);
      console.log(`Found ${nearbyOptions.length} options`);
      
      if (nearbyOptions.length > 0) {
        // Group by expiry and show first few
        const nearbyByExpiry = {};
        nearbyOptions.forEach(option => {
          const expiry = option.SM_EXPIRY_DATE;
          if (!nearbyByExpiry[expiry]) {
            nearbyByExpiry[expiry] = [];
          }
          nearbyByExpiry[expiry].push(option);
        });
        
        const firstExpiry = Object.keys(nearbyByExpiry).sort()[0];
        if (firstExpiry) {
          console.log(`\nFirst expiry (${firstExpiry}) nearby options:`);
          nearbyByExpiry[firstExpiry].slice(0, 10).forEach(option => {
            console.log(`   ${option.SYMBOL_NAME} - ID: ${option.SECURITY_ID}`);
          });
        }
      }
    }
  })
  .on('error', (error) => {
    console.error('❌ Error reading CSV:', error);
  });
